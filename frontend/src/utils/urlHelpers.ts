export const ensureHttps = (url: string): string => {
  if (!url) return url;
  
  // Check if URL already has a protocol
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url.replace('http://', 'https://');
  }
  
  // Add https:// if no protocol is present
  return `https://${url}`;
};

export const isValidUrl = (url: string): boolean => {
  try {
    new URL(ensureHttps(url));
    return true;
  } catch (e) {
    return false;
  }
};

// Changed to handle any URL, but keeping the function name for compatibility
export const isGithubUrl = (url: string): boolean => {
  return isValidUrl(url);
}; 