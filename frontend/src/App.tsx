import React from "react";
import { Route, Routes, Navigate, useLocation } from "react-router-dom";
import { ThemeProvider } from "@mui/material/styles";
import CssBaseline from "@mui/material/CssBaseline";
import { AuthProvider } from "./contexts/AuthContext";
import AuthGuard from "./components/AuthGuard";
import { AnimatePresence } from "framer-motion";
import { theme } from "./theme";

// Pages
import Landing from "./pages/Landing";
import Dashboard from "./pages/Dashboard";
import Login from "./pages/Login";
import Register from "./pages/Register";
import Profile from "./pages/Profile";
import EvaluationPage from "./pages/EvaluationPage";
import Projects from "./pages/Projects";
import PendingApproval from "./pages/PendingApproval";
import EducatorDashboard from "./pages/EducatorDashboard";
import CreateClass from "./pages/CreateClass";
import EducatorGuard from "./components/EducatorGuard";

import ForgotPassword from "./pages/ForgotPassword";
import ResetPassword from "./pages/ResetPassword";
import EmailVerification from "./pages/EmailVerification";
import AdminApproval from "./pages/AdminApproval";

const App: React.FC = () => {
  const location = useLocation();

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AuthProvider>
        <AnimatePresence mode="wait">
          <Routes location={location} key={location.pathname}>
            {/* Public routes */}
            <Route path="/" element={<Landing />} />
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />
            <Route path="/forgot-password" element={<ForgotPassword />} />
            <Route path="/reset-password/:token" element={<ResetPassword />} />
            <Route path="/approval" element={<PendingApproval />} />
            <Route path="/verify-email/:code" element={<EmailVerification />} />
            <Route path="/admin-approve/:code" element={<AdminApproval />} />

            {/* Protected routes */}
            <Route
              path="/dashboard"
              element={
                <AuthGuard>
                  <Dashboard />
                </AuthGuard>
              }
            />
            <Route
              path="/educator-dashboard"
              element={
                <EducatorGuard>
                  <EducatorDashboard />
                </EducatorGuard>
              }
            />
            <Route
              path="/create-class"
              element={
                <EducatorGuard>
                  <CreateClass />
                </EducatorGuard>
              }
            />
            <Route
              path="/profile"
              element={
                <AuthGuard>
                  <Profile />
                </AuthGuard>
              }
            />
            <Route
              path="/evaluation/:id"
              element={
                <AuthGuard>
                  <EvaluationPage />
                </AuthGuard>
              }
            />
            <Route
              path="/projects"
              element={
                <AuthGuard>
                  <Projects />
                </AuthGuard>
              }
            />

            {/* Fallback route */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </AnimatePresence>
      </AuthProvider>
    </ThemeProvider>
  );
};

export default App;
