import React from 'react';
import { Box, Container, Typography, Link } from '@mui/material';

const Footer: React.FC = () => {
  return (
    <Box 
      component="footer" 
      sx={{ 
        py: 6,
        bgcolor: '#0a192f',
        borderTop: '1px solid rgba(100, 255, 218, 0.1)',
        position: 'relative',
        zIndex: 1,
      }}
    >
      <Container maxWidth="lg">
        <Box sx={{ display: 'flex', justifyContent: 'space-between', flexWrap: 'wrap', gap: 4 }}>
          <Box sx={{ flex: '1', minWidth: '200px' }}>
            <Typography
              variant="h6"
              sx={{
                background: 'linear-gradient(45deg, #64FFDA, #7B89F4)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                mb: 2,
              }}
            >
              DevSpace
            </Typography>
            <Typography variant="body2" sx={{ color: '#8892b0' }}>
              Empowering developers through project-based learning and peer collaboration.
            </Typography>
          </Box>
          
          <Box sx={{ flex: '1', minWidth: '200px' }}>
            <Typography variant="h6" sx={{ color: '#64FFDA' }} gutterBottom>
              Quick Links
            </Typography>
            <Link href="#features" sx={{ color: '#8892b0', display: 'block', mb: 1 }}>
              Features
            </Link>
            <Link href="#tracks" sx={{ color: '#8892b0', display: 'block', mb: 1 }}>
              Learning Tracks
            </Link>
            <Link href="#faq" sx={{ color: '#8892b0', display: 'block' }}>
              FAQ
            </Link>
          </Box>
        </Box>
        
        <Box 
          sx={{ 
            mt: 4, 
            pt: 2,
            borderTop: '1px solid rgba(255, 255, 255, 0.1)',
            textAlign: 'center'
          }}
        >
          <Typography variant="body2" sx={{ color: '#8892b0' }}>
            © {new Date().getFullYear()} DevSpace. All rights reserved.
          </Typography>
        </Box>
      </Container>
    </Box>
  );
};

export default Footer;