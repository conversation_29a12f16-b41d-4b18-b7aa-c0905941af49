import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  List,
  ListItem,
  ListItemText,
  Typography,
  Button,
  Box,
  Paper,
  Divider,
  Grid,
  Card,
  CardContent,
  Chip,
  useTheme,
  Modal,
  IconButton,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  TextField,
} from "@mui/material";
import EditIcon from "@mui/icons-material/Edit";
import AssignmentIcon from "@mui/icons-material/Assignment";
import EmojiEventsIcon from "@mui/icons-material/EmojiEvents";
import api from "../../services/api";
import { Course, Project, User } from "../../types/index";
import { useAuth } from "../../contexts/AuthContext";
import ProjectView from "@/components/ProjectView";
import SubmissionStatus from "@/components/SubmissionStatus";

interface ProjectInPool {
  id: number;
  project: Project;
  status: string;
  created_at: string;
  submitted_by: User;
  submitted_by_details: {
    id: number;
    username: string;
    email: string;
    full_name: string;
    track: string | null;
    submitted_at: string;
  };
}

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  useTheme();
  const { user, fetchUser } = useAuth();
  const [projects, setProjects] = useState<ProjectInPool[]>([]);
  const [loading, setLoading] = useState(true);
  const [nextProject, setNextProject] = useState<Project | null>(null);
  const [activeSubmissions, setActiveSubmissions] = useState<ProjectInPool[]>(
    []
  );
  const [openCourseModal, setOpenCourseModal] = useState(false);
  const [availableCourses, setAvailableCourses] = useState<Course[]>([]);
  const [selectedCourse, setSelectedCourse] = useState<string>("");
  const [modalSelectedCourse, setModalSelectedCourse] = useState<string>("");
  const [courseCode, setCourseCode] = useState("");

  useEffect(() => {
    fetchUser();
    fetchProjects();
    fetchActiveSubmissions();
    fetchAvailableCourses();
  }, []);

  useEffect(() => {
    if (selectedCourse) {
      fetchNextProject();
    }
  }, [selectedCourse]);

  // Sync selectedCourse with user's current_course when user data changes
  useEffect(() => {
    if (
      user?.current_course?.code &&
      user.current_course.code !== selectedCourse
    ) {
      setSelectedCourse(user.current_course.code);
      setModalSelectedCourse(user.current_course.code);
    }
  }, [user?.current_course?.code]);

  const fetchAvailableCourses = async () => {
    try {
      const response = await api.get<Course[]>("/projects/courses/");
      setAvailableCourses(response.data);

      // Only set initial course selection if not already set
      if (!selectedCourse) {
        if (user?.current_course?.code) {
          setSelectedCourse(user.current_course.code);
          setModalSelectedCourse(user.current_course.code);
        } else if (response.data.length > 0) {
          setSelectedCourse(response.data[0].code);
          setModalSelectedCourse(response.data[0].code);
        }
      }
    } catch (error) {
      console.error("Error fetching courses:", error);
      setAvailableCourses([]);
      setSelectedCourse("");
      setModalSelectedCourse("");
    } finally {
      setLoading(false);
    }
  };

  const handleCourseChange = async () => {
    setLoading(true);
    try {
      console.log("Updating course to:", modalSelectedCourse);

      // Update the course in the backend
      const response = await api.put(`/users/profile/`, {
        current_course: modalSelectedCourse,
      });
      console.log("Course update response:", response.data);

      // Update local state
      setSelectedCourse(modalSelectedCourse);

      // Refresh user data and course-dependent data
      await Promise.all([
        fetchUser(),
        fetchProjects(),
        fetchActiveSubmissions(),
      ]);

      console.log("Course updated successfully to:", modalSelectedCourse);

      // Close modal
      setOpenCourseModal(false);
    } catch (error) {
      console.error("Error updating course:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchProjects = async () => {
    try {
      const response = await api.get<ProjectInPool[]>(`/projects/pool/`);
      setProjects(response.data);
    } catch (error) {
      console.error("Error fetching evaluation pool:", error);
      setProjects([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchNextProject = async () => {
    if (!selectedCourse) {
      setNextProject(null);
      return;
    }
    try {
      const response = await api.get(
        `/projects/courses/${selectedCourse}/next/`
      );
      setNextProject(response.data);
    } catch (error) {
      console.error("Error fetching next project:", error);
      setNextProject(null);
    } finally {
      setLoading(false);
    }
  };

  const handleEvaluate = async (projectId: number) => {
    try {
      navigate(`/evaluation/${projectId}`);
    } catch (error) {
      console.error("Error evaluating project:", error);
    }
  };

  const fetchActiveSubmissions = async () => {
    try {
      // const response = await api.get<ProjectInPool[]>(
      //   "/projects/my-submissions/"
      // );
      const response = await api.get<ProjectInPool[]>(
        "/projects/my-submissions/"
      );
      const activeSubmissionsData = response.data.filter((sub) =>
        ["pending", "in_evaluation"].includes(sub.status)
      );

      // Only update state if there are changes
      if (
        JSON.stringify(activeSubmissionsData) !==
        JSON.stringify(activeSubmissions)
      ) {
        setActiveSubmissions(activeSubmissionsData);
      }
    } catch (error) {
      console.error("Error fetching active submissions:", error);
    }
  };

  const handleProjectSubmitSuccess = async () => {
    await Promise.all([
      fetchUser(),
      fetchProjects(),
      fetchNextProject(),
      fetchActiveSubmissions(),
    ]);
  };

  const handleCourseRegister = async () => {
    try {
      const response = await api.post(
        `projects/courses/register/${courseCode.trim()}/`
      );
      console.log(response.data);
      await fetchAvailableCourses();
      setCourseCode("");
      setOpenCourseModal(false);
    } catch (error) {
      console.error("Error registering for course:", error);
    }
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <>
      {/* User Stats Section */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={4}>
          <Card
            sx={{
              height: "100%",
              background:
                "linear-gradient(135deg, rgba(100, 255, 218, 0.15), rgba(123, 137, 244, 0.15))",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(100, 255, 218, 0.2)",
            }}
          >
            <CardContent>
              <Typography
                variant="h6"
                gutterBottom
                sx={{ color: "text.primary" }}
              >
                Welcome back, {user?.first_name}!
              </Typography>
              <Box sx={{ display: "flex", alignItems: "center", mt: 2 }}>
                <EmojiEventsIcon
                  sx={{ fontSize: 40, mr: 2, color: "primary.main" }}
                />
                <Box>
                  <Typography variant="body2" sx={{ color: "text.secondary" }}>
                    Current Level
                  </Typography>
                  <Typography variant="h4" sx={{ color: "text.primary" }}>
                    {user?.level}
                  </Typography>
                </Box>
              </Box>
              <Box sx={{ display: "flex", alignItems: "center", mt: 2 }}>
                <Typography variant="body2" sx={{ color: "text.secondary" }}>
                  Current Course
                </Typography>
                <Box sx={{ display: "flex", alignItems: "center", ml: 2 }}>
                  <Chip
                    label={
                      user?.current_course?.name ||
                      availableCourses.find(
                        (course) => course.code === selectedCourse
                      )?.name ||
                      "No course selected"
                    }
                    sx={{
                      bgcolor: "rgba(123, 137, 244, 0.2)",
                      color: "text.primary",
                    }}
                  />
                  <IconButton
                    size="small"
                    onClick={() => {
                      // Set modal selection to current course when opening
                      setModalSelectedCourse(
                        user?.current_course?.code || selectedCourse
                      );
                      setOpenCourseModal(true);
                    }}
                    sx={{ ml: 1, color: "primary.main" }}
                  >
                    <EditIcon fontSize="small" />
                  </IconButton>
                </Box>
              </Box>

              {/* Course Selection Modal */}
              <Modal
                open={openCourseModal}
                onClose={() => setOpenCourseModal(false)}
                aria-labelledby="course-selection-modal"
              >
                <Box
                  sx={{
                    position: "absolute",
                    top: "50%",
                    left: "50%",
                    transform: "translate(-50%, -50%)",
                    width: { xs: "90%", sm: 400 },
                    bgcolor: "background.paper",
                    borderRadius: 2,
                    boxShadow: 24,
                    p: 4,
                  }}
                >
                  <Typography variant="h6" component="h2" gutterBottom>
                    {availableCourses.length > 0
                      ? "Select Course"
                      : "Register for Course"}
                  </Typography>
                  {availableCourses.length > 0 ? (
                    <FormControl
                      component="fieldset"
                      sx={{
                        maxHeight: "calc(80vh - 120px)",
                        overflowY: "auto",
                        mb: 2,
                        width: "100%",
                      }}
                    >
                      <RadioGroup
                        value={modalSelectedCourse}
                        onChange={(e) => setModalSelectedCourse(e.target.value)}
                      >
                        {availableCourses.map((course) => (
                          <FormControlLabel
                            key={`course-${course.code || course.name}`}
                            value={course.code}
                            control={<Radio />}
                            label={`${course.name} (${course.code})`}
                          />
                        ))}
                      </RadioGroup>
                    </FormControl>
                  ) : (
                    <Box sx={{ mt: 2 }}>
                      <TextField
                        fullWidth
                        label="Course Code"
                        value={courseCode}
                        onChange={(e) => setCourseCode(e.target.value)}
                        placeholder="Enter course code"
                        sx={{ mb: 2 }}
                      />
                      <Button
                        variant="contained"
                        fullWidth
                        onClick={handleCourseRegister}
                        disabled={!courseCode}
                      >
                        Register
                      </Button>
                    </Box>
                  )}
                  <Box
                    sx={{
                      mt: 3,
                      display: "flex",
                      justifyContent: "flex-end",
                      gap: 2,
                    }}
                  >
                    <Button
                      onClick={() => {
                        setOpenCourseModal(false);
                        setModalSelectedCourse(selectedCourse);
                        setCourseCode("");
                      }}
                    >
                      Cancel
                    </Button>
                    {availableCourses.length > 0 && (
                      <Button
                        variant="contained"
                        onClick={handleCourseChange}
                        disabled={!modalSelectedCourse}
                      >
                        Save
                      </Button>
                    )}
                  </Box>
                </Box>
              </Modal>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card
            sx={{
              height: "100%",
              background:
                "linear-gradient(135deg, rgba(123, 137, 244, 0.15), rgba(100, 255, 218, 0.15))",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(123, 137, 244, 0.2)",
            }}
          >
            <CardContent>
              <Typography
                variant="h6"
                gutterBottom
                sx={{ color: "text.primary" }}
              >
                Available Points
              </Typography>
              <Box sx={{ display: "flex", alignItems: "center", mt: 2 }}>
                <AssignmentIcon
                  sx={{ fontSize: 40, mr: 2, color: "secondary.main" }}
                />
                <Typography variant="h4" sx={{ color: "text.primary" }}>
                  {user?.points}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card
            sx={{
              height: "100%",
              background:
                "linear-gradient(135deg, rgba(100, 255, 218, 0.15), rgba(100, 255, 218, 0.05))",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(100, 255, 218, 0.2)",
            }}
          >
            <CardContent>
              <Typography
                variant="h6"
                gutterBottom
                sx={{ color: "text.primary" }}
              >
                Projects to Evaluate
              </Typography>
              <Box sx={{ display: "flex", alignItems: "center", mt: 2 }}>
                <AssignmentIcon
                  sx={{ fontSize: 40, mr: 2, color: "success.light" }}
                />
                <Typography variant="h4" sx={{ color: "text.primary" }}>
                  {projects.length}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Active Submissions Section */}
      {activeSubmissions.length > 0 && (
        <Paper
          elevation={2}
          sx={{
            p: 4,
            mt: 4,
            background:
              "linear-gradient(135deg, rgba(100, 255, 218, 0.1), rgba(123, 137, 244, 0.1))",
            border: "1px solid rgba(100, 255, 218, 0.2)",
          }}
        >
          <Typography variant="h5" gutterBottom>
            Active Submissions
          </Typography>
          {activeSubmissions.map((submission) => (
            <Box key={submission.id} sx={{ mb: 3 }}>
              <Typography variant="h6" color="primary" gutterBottom>
                {submission.project.title}
              </Typography>
              <SubmissionStatus
                submissionId={submission.id}
                onStatusChange={fetchActiveSubmissions}
              />
            </Box>
          ))}
        </Paper>
      )}

      {/* Next Project Box */}
      <Paper
        elevation={2}
        sx={{
          p: 4,
          mt: 4,
          background:
            "linear-gradient(135deg, rgba(100, 255, 218, 0.1), rgba(123, 137, 244, 0.1))",
          border: "1px solid rgba(100, 255, 218, 0.2)",
        }}
      >
        <Typography variant="h5" gutterBottom>
          Next Available Project
        </Typography>
        {nextProject ? (
          <ProjectView
            project={nextProject}
            onSubmit={fetchNextProject}
            onSubmitSuccess={handleProjectSubmitSuccess}
          />
        ) : (
          <Typography variant="body1" color="text.secondary">
            No projects available at your current level. Keep evaluating to
            level up!
          </Typography>
        )}
      </Paper>

      {/* Evaluation Pool Section */}
      <Paper elevation={2} sx={{ p: 3, mt: 4 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 3,
          }}
        >
          <Typography variant="h5" component="h2">
            Evaluation Pool
          </Typography>
          <Chip
            label={`${projects.length} Submissions Available`}
            color="primary"
            variant="outlined"
          />
        </Box>
        <Divider sx={{ mb: 2 }} />
        <List>
          {projects.map((submission, index) => (
            <React.Fragment key={submission.id}>
              {index > 0 && <Divider />}
              <ListItem
                sx={{
                  py: 2,
                  "&:hover": {
                    bgcolor: "action.hover",
                  },
                }}
              >
                <ListItemText
                  primary={
                    <Typography variant="h6" component="div">
                      {submission.project?.title || "Unknown Project"}
                    </Typography>
                  }
                  secondary={
                    <Typography component="div">
                      <Box
                        sx={{
                          display: "flex",
                          flexDirection: "column",
                          gap: 1,
                          mt: 1,
                        }}
                      >
                        <Box
                          sx={{
                            display: "flex",
                            gap: 1,
                            alignItems: "center",
                          }}
                        >
                          <Chip
                            label={`${
                              submission.project?.points_required || 0
                            } Points Required`}
                            size="small"
                            color="secondary"
                          />
                          <Typography
                            variant="caption"
                            color="text.secondary"
                            component="span"
                          >
                            Submitted by{" "}
                            {submission.submitted_by_details.full_name}
                          </Typography>
                        </Box>
                        <Box
                          sx={{
                            display: "flex",
                            gap: 2,
                            alignItems: "center",
                          }}
                        >
                          <Typography
                            variant="caption"
                            color="text.secondary"
                            component="span"
                          >
                            Submitted:{" "}
                            {new Date(
                              submission.submitted_by_details.submitted_at
                            ).toLocaleDateString()}
                          </Typography>
                          <Box sx={{ display: "flex", gap: 1 }}>
                            {submission.submitted_by_details.track && (
                              <Chip
                                label={`Track: ${submission.submitted_by_details.track}`}
                                size="small"
                                variant="outlined"
                                color="secondary"
                              />
                            )}
                          </Box>
                        </Box>
                      </Box>
                    </Typography>
                  }
                />
                <Button
                  variant="contained"
                  color="primary"
                  onClick={() => handleEvaluate(submission.id)}
                  disabled={submission.submitted_by.id === user?.id}
                  sx={{ ml: 2, minWidth: 120 }}
                >
                  {submission.submitted_by.id === user?.id
                    ? "Your Submission"
                    : "Evaluate"}
                </Button>
              </ListItem>
            </React.Fragment>
          ))}
          {projects.length === 0 && (
            <ListItem>
              <ListItemText
                primary={
                  <Box
                    component="div"
                    sx={{
                      color: "text.secondary",
                      typography: "body1",
                      textAlign: "center",
                    }}
                  >
                    No submissions available for evaluation
                  </Box>
                }
              />
            </ListItem>
          )}
        </List>
      </Paper>
    </>
  );
};

export default Dashboard;
