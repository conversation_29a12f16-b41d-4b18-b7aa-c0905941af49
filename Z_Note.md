frontend fix suggestions:
# Student registration
* when a student registers to a course with the course_code, he is going into pending status. The frontend should reflect this status to the user.
* when a student is already in a pending status, and he tries to register again, he should get a message that he is already in pending status. right now I am returning a 409 error.
* when a student is already enrolled in a course, and he tries to register again, he should get a message that he is already enrolled. right now I am returning a 409 error.
* when a student tries to register for a course that does not exist, he should get a message that the course does not exist. right now I am returning a 404 error.

# Educator approving/rejecting registration
* when a student is in pending status, the educator should be able to see the student's name and the course name in the list of pending registrations.
* when an educator approves a registration, the student should be enrolled in the course. The frontend should reflect this status to the user.
* when an educator tries to approve a registration that does not exist, he should get a message that the registration does not exist. right now I am returning a 404 error.