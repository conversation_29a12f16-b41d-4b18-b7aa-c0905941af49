import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
export default defineConfig({
    plugins: [react()],
    resolve: {
        alias: {
            '@': path.resolve(__dirname, './src'),
        },
    },
    server: {
        host: false,
        port: 3000,
    },
    build: {
        outDir: 'dist',
        sourcemap: false,
        minify: true,
        chunkSizeWarningLimit: 1000,
    }
});
