import pytest
import shutil
from pathlib import Path
from django.core.files.uploadedfile import SimpleUploadedFile
from django.db import IntegrityError
from django.conf import settings
from django.contrib.auth import get_user_model
from .models import User, Track

@pytest.fixture(autouse=True)
def setup_test_environment(tmp_path):
    """Set up test environment with local file storage."""
    media_test_dir = Path(settings.MEDIA_ROOT)
    media_test_dir.mkdir(parents=True, exist_ok=True)
    
    yield
    
    # Cleanup test media files after test
    if media_test_dir.exists():
        shutil.rmtree(media_test_dir)

@pytest.fixture
def profile_picture():
    return SimpleUploadedFile(
        "test_profile.jpg",
        b"file_content",
        content_type="image/jpeg"
    )

@pytest.fixture
def track():
    return Track.objects.create(
        name="Web Development",
        description="Web Development Track"
    )

@pytest.fixture
def course(track):
    from projects.models import Course
    return Course.objects.create(
        code="TEST101",
        name="Test Course",
        description="A test course",
        track=track
    )

@pytest.fixture
def user_data():
    return {
        'username': 'testuser',
        'email': '<EMAIL>',
        'first_name': 'Test',
        'last_name': 'User',
        'password': 'testpass123'
    }

@pytest.fixture
def user(user_data):
    user_data['user_type'] = 'S'  # Set as Student
    return User.objects.create_user(**user_data)

@pytest.fixture
def another_user():
    user_data = {
        'username': 'anotheruser',
        'email': '<EMAIL>',
        'first_name': 'Another',
        'last_name': 'User',
        'password': 'testpass123',
        'user_type': 'S'  # Set as Student
    }
    return User.objects.create_user(**user_data)

# Track Tests
@pytest.mark.django_db(transaction=True)
class TestTrack:
    def test_track_creation(self, track):
        assert track.name == "Web Development"
        assert track.description == "Web Development Track"
        assert track.created_at is not None

    def test_track_str(self, track):
        assert str(track) == "Web Development"

    @pytest.mark.django_db(transaction=True)
    def test_track_unique_name(self):
        Track.objects.create(name="Unique Track")
        with pytest.raises(IntegrityError):
            Track.objects.create(name="Unique Track")

    def test_track_ordering(self):
        Track.objects.create(name="A Track")
        Track.objects.create(name="B Track")
        Track.objects.create(name="C Track")
        tracks = Track.objects.all()
        assert tracks[0].name == "A Track"
        assert tracks[1].name == "B Track"
        assert tracks[2].name == "C Track"

    def test_track_blank_description(self):
        track = Track.objects.create(name="No Description Track")
        assert track.description == ""

# User Tests
@pytest.mark.django_db(transaction=True)
class TestUser:
    def test_user_creation(self, user):
        assert user.username == "testuser"
        assert user.email == "<EMAIL>"
        assert user.first_name == "Test"
        assert user.last_name == "User"
        assert user.user_type == "S"  # Student
        assert user.points == 2  # default value
        assert user.level == 1   # default value
        assert user.exp == 0   # default value
        assert not user.is_approved
        assert not user.is_email_verified

    def test_user_str(self, user):
        assert str(user) == "testuser"

    @pytest.mark.django_db(transaction=True)
    def test_user_email_unique(self, user):
        with pytest.raises(IntegrityError):
            User.objects.create_user(
                username="another_test",
                email=user.email,  # Same email
                password="testpass123"
            )

    def test_user_increase_level(self, user):
        initial_level = user.level
        user.increase_level()
        assert user.level == initial_level + 1
        
        # Test multiple increases
        user.increase_level()
        user.increase_level()
        assert user.level == initial_level + 3

    def test_user_without_course(self):
        user = User.objects.create_user(
            username="no_track",
            email="<EMAIL>",
            password="testpass123",
            user_type='S'
        )
        assert list(user.enrolled_courses.all()) == []
        assert user.points == 2  # default value
        assert user.level == 1   # default value
        assert user.exp == 0  # default value

    def test_user_profile_picture_path(self, profile_picture):
        user = User.objects.create_user(
            username="picture_test",
            email="<EMAIL>",
            password="testpass123",
            profile_picture=profile_picture
        )
        # Check if file is saved in the correct directory
        assert 'profile_pictures' in user.profile_picture.name
        # Check if file has correct extension
        assert '.jpg' in user.profile_picture.name

    @pytest.mark.django_db(transaction=True)
    def test_user_approval_codes(self):
        user = User.objects.create_user(
            username="codes_test",
            email="<EMAIL>",
            password="testpass123",
            approval_code="test_approval_code",
            admin_approval_code="test_admin_code"
        )
        assert user.approval_code == "test_approval_code"
        assert user.admin_approval_code == "test_admin_code"

        # Test unique constraint
        with pytest.raises(IntegrityError):
            User.objects.create_user(
                username="another_codes_test",
                email="<EMAIL>",
                password="testpass123",
                approval_code="test_approval_code"  # Same approval code
            )

    @pytest.mark.parametrize("field,value", [
        ("is_approved", True),
        ("is_email_verified", True),
        ("points", 100),
        ("level", 5)
    ])
    def test_user_field_updates(self, user, field, value):
        setattr(user, field, value)
        user.save()
        user.refresh_from_db()
        assert getattr(user, field) == value

    def test_user_required_fields(self):
        # Test missing required fields
        with pytest.raises(ValueError):
            User.objects.create_user(
                username="",
                email="<EMAIL>",
                password="testpass123"
            )
        with pytest.raises(ValueError):
            User.objects.create_user(
                username="test",
                email="",
                password="testpass123"
            )
        # Test missing password
        with pytest.raises(ValueError):
            User.objects.create_user(
                username="test",
                email="<EMAIL>",
                password=""
            )

    def test_user_track_cascade(self, user, track, course):
        track_id = track.id
        user.enrolled_courses.add(course)
        
        # When a track is deleted, its courses should be deleted
        track.delete()
        
        user.refresh_from_db()
        assert list(user.enrolled_courses.all()) == []  # User's enrolled courses should be empty


@pytest.mark.django_db(transaction=True)
class TestUserLevelProgression:
    def test_initial_user_level_points(self, user):
        """Test that new users start with correct default values"""
        assert user.level == 1
        assert user.points == 2
        assert user.exp == 0

    def test_user_level_up(self, user):
        """Test that user level can be increased"""
        initial_level = user.level
        user.increase_level()
        assert user.level == initial_level + 1
        
        # Level should never decrease
        # Create a copy of the user's current level
        current_level = user.level
        # Try to decrease level manually (which shouldn't be allowed)
        user.level -= 1
        user.save()
        user.refresh_from_db()
        # The level should not have decreased
        assert user.level == current_level
    
    def test_user_point_accumulation(self, user):
        """Test that user points can be accumulated"""
        initial_points = user.points
        user.points += 5
        user.save()
        user.refresh_from_db()
        assert user.points == initial_points + 5

    def test_user_exp_accumulation(self, user):
        """Test that user experience can be accumulated"""
        initial_exp = user.exp
        user.exp += 100
        user.save()
        user.refresh_from_db()
        assert user.exp == initial_exp + 100
    
    def test_level_persistence_across_sessions(self, user):
        """Test that user level persists across sessions"""
        user.level = 5
        user.save()
        # Simulate user logging out and back in
        user_id = user.id
        del user
        reloaded_user = User.objects.get(id=user_id)
        assert reloaded_user.level == 5


@pytest.mark.django_db(transaction=True)
class TestStudentEducatorInteractions:
    @pytest.fixture
    def student(self):
        return User.objects.create_user(
            username='student',
            email='<EMAIL>',
            password='testpass123',
            user_type='S'
        )
    
    @pytest.fixture
    def educator(self):
        return User.objects.create_user(
            username='educator',
            email='<EMAIL>',
            password='testpass123',
            user_type='E'
        )
    
    @pytest.fixture
    def course_with_educator(self, track, educator):
        from projects.models import Course
        return Course.objects.create(
            code="EDU101",
            name="Educator Course",
            description="A course with an educator",
            educator=educator,
            track=track
        )
    
    def test_educator_course_creation(self, educator, track):
        """Test that an educator can create a course"""
        from projects.models import Course
        course = Course.objects.create(
            name="New Educator Course",
            description="A new course by educator",
            educator=educator,
            track=track
        )
        assert course.educator == educator
    
    def test_student_course_enrollment(self, student, course_with_educator):
        """Test that a student can enroll in a course"""
        student.enrolled_courses.add(course_with_educator)
        assert course_with_educator in student.enrolled_courses.all()
    
    def test_course_registration_request(self, student, course_with_educator):
        """Test course registration request workflow"""
        from projects.models import CourseRegistrationRequest
        request = CourseRegistrationRequest.objects.create(
            student=student,
            course=course_with_educator,
            status='pending'
        )
        assert request.status == 'pending'
        
        # Approve the request
        request.status = 'approved'
        request.save()
        assert request.status == 'approved'
        
        # Enroll the student (would be done in the view)
        student.enrolled_courses.add(course_with_educator)
        assert course_with_educator in student.enrolled_courses.all()
    
    def test_educator_cannot_enroll_as_student(self, educator, course_with_educator):
        """Test that an educator cannot enroll as a student in their own course"""
        from projects.models import CourseRegistrationRequest
        from django.core.exceptions import ValidationError
        
        # Custom validation to prevent educators from enrolling in their own courses
        def validate_enrollment():
            if course_with_educator.educator == educator:
                raise ValidationError("Educators cannot enroll as students in their own courses")
        
        # Execute validation, which should raise an exception
        with pytest.raises((ValidationError, Exception)):
            validate_enrollment()
            # This would normally be prevented at the view level
            CourseRegistrationRequest.objects.create(
                student=educator,  # educator trying to be a student
                course=course_with_educator,  # in their own course
                status='pending'
            )
    
    def test_student_submission_peer_evaluation(self, student, educator, course_with_educator):
        """Test submission and peer evaluation workflow"""
        from projects.models import Project, ProjectSubmission, Evaluation
        import tempfile
        from django.core.files.uploadedfile import SimpleUploadedFile
        
        # Enroll student and educator in course
        student.enrolled_courses.add(course_with_educator)
        educator.enrolled_courses.add(course_with_educator)
        
        # Create a project
        test_pdf = SimpleUploadedFile(
            "test.pdf",
            b"file_content",
            content_type="application/pdf"
        )
        project = Project.objects.create(
            title="Test Project",
            description="Test Description",
            pdf_file=test_pdf,
            points_required=5,
            course=course_with_educator
        )
        
        # Student has enough points
        student.points = 10
        student.save()
        
        # Submit a project
        submission = ProjectSubmission.objects.create(
            project=project,
            submitted_by=student,
            github_repo="https://github.com/test/repo",
            submission_type="github"
        )
        
        # Another student evaluates (peer evaluation)
        peer_student = get_user_model().objects.create_user(
            username='peer_student',
            email='<EMAIL>',
            password='testpass123',
            user_type='S'
        )
        peer_student.enrolled_courses.add(course_with_educator)
        
        evaluation = Evaluation.objects.create(
            submission=submission,
            evaluator=peer_student,  # A student peer evaluates, not the educator
            comments="Good work, but needs improvement in some areas.",
            score=85.0
        )
        
        assert evaluation.evaluator == peer_student
        assert evaluation.submission.submitted_by == student


@pytest.mark.django_db(transaction=True)
class TestUserAuthenticationAndPermissions:
    def test_user_approval_flow(self, user):
        """Test the user approval flow"""
        assert not user.is_approved
        assert not user.is_email_verified
        
        # Simulate email verification
        user.is_email_verified = True
        user.save()
        user.refresh_from_db()
        assert user.is_email_verified
        
        # Simulate admin approval
        user.is_approved = True
        user.save()
        user.refresh_from_db()
        assert user.is_approved
    
    def test_permission_check(self, user):
        """Test the IsApprovedUser permission"""
        from core.permissions import IsApprovedUser
        from django.http import HttpRequest
        from rest_framework.request import Request
        
        # Create a custom user check
        def check_approval_permission(a_user):
            return a_user.is_approved and a_user.is_email_verified
        
        # Verify initially that user doesn't meet the approval criteria
        assert not check_approval_permission(user)
        
        # Approve and verify user
        user.is_approved = True
        user.is_email_verified = True
        user.save()
        
        # Should now satisfy the approval criteria
        user_from_db = type(user).objects.get(pk=user.pk)
        assert check_approval_permission(user_from_db)
    
    def test_password_validation(self):
        """Test password validation utility function"""
        from users.utils import validate_password
        
        # Test invalid passwords
        assert not validate_password("short")[0]
        assert not validate_password("12345678")[0]
        assert not validate_password("lowercase")[0]
        assert not validate_password("UPPERCASE")[0]
        assert not validate_password("nodigits")[0]
        assert not validate_password("password123")[0]
        
        # Test valid password
        assert validate_password("ValidP@ss123")[0]
        
        # Test password with user data
        user_data = {'email': '<EMAIL>', 'first_name': 'John'}
        assert not validate_password("John123456", user_data)[0]
    
    def test_user_type_permissions(self):
        """Test different permissions for different user types"""
        student = User.objects.create_user(
            username='student_perm',
            email='<EMAIL>',
            password='Testpass123',
            user_type='S'
        )
        
        educator = User.objects.create_user(
            username='educator_perm',
            email='<EMAIL>',
            password='Testpass123',
            user_type='E'
        )
        
        # Verify user types
        assert student.user_type == 'S'
        assert educator.user_type == 'E'
