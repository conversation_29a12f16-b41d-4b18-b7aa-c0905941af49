import os
import sys
from django.core.management.base import BaseCommand
from django.conf import settings
from pathlib import Path

class Command(BaseCommand):
    help = 'Clean up test files from media directory'

    def handle(self, *args, **kwargs):                
        # Attempt to import the cleanup function
        try:
            from core.storages import cleanup_test_files
            directories = ['projects/pdfs', 'projects/submissions']
            for directory in directories:
                cleanup_test_files(directory)
            self.stdout.write(
                self.style.SUCCESS(f'Successfully cleaned up test files in {directory}')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error during cleanup: {e}')
            )
