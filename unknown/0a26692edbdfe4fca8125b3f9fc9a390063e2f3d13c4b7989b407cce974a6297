import React from "react";
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Paper,
  TextField,
  IconButton,
  useTheme,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import AddIcon from "@mui/icons-material/Add";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";

export interface Question {
  id: string;
  text: string;
  type: "binary";
  weight: number;
}

export interface PDFEvaluation {
  pdfId: string;
  questions: Question[];
  totalWeight: number;
}

export interface EvaluationSheet {
  pdfEvaluations: PDFEvaluation[];
  totalWeight: number; // Should always sum to 100
  errors?: {
    weightValidation?: string;
    [key: string]: string | undefined;
  };
}

interface EvaluationSheetManagementProps {
  evaluationSheet: EvaluationSheet;
  pdfs: Array<{ id: string; title: string }>;
  onEvaluationSheetChange: (sheet: EvaluationSheet) => void;
  errors: { [key: string]: string };
}

const EvaluationSheetManagement: React.FC<EvaluationSheetManagementProps> = ({
  evaluationSheet,
  pdfs,
  onEvaluationSheetChange,
  errors,
}) => {
  const theme = useTheme();
  const recalculateWeights = (
    evaluations: PDFEvaluation[]
  ): PDFEvaluation[] => {
    const allQuestions = evaluations.flatMap((pe) => pe.questions);
    const totalQuestions = allQuestions.length;

    if (totalQuestions === 0) return evaluations;

    // Keep existing weights if they sum to 100, otherwise distribute evenly
    const totalExistingWeight = allQuestions.reduce(
      (sum, q) => sum + q.weight,
      0
    );

    if (Math.abs(totalExistingWeight - 100) < 0.01) {
      // Weights are already perfect, just update the totalWeight for each PDF
      return evaluations.map((pdfEval) => ({
        ...pdfEval,
        totalWeight: pdfEval.questions.reduce((sum, q) => sum + q.weight, 0),
      }));
    }

    // If weights need adjustment, first distribute evenly
    const defaultWeight = 100 / totalQuestions;
    let result = evaluations.map((pdfEval) => ({
      ...pdfEval,
      questions: pdfEval.questions.map((q) => ({
        ...q,
        weight: defaultWeight,
      })),
      totalWeight: pdfEval.questions.length * defaultWeight,
    }));

    // Check if we need tiny adjustment due to division rounding
    const totalAfterDistribution = result.reduce(
      (sum, pdfEval) =>
        sum + pdfEval.questions.reduce((s, q) => s + q.weight, 0),
      0
    );

    const diff = 100 - totalAfterDistribution;
    if (Math.abs(diff) > 0.001) {
      // If there's any meaningful difference
      // Find the question with the largest weight to adjust
      const allUpdatedQuestions = result.flatMap((pe) => pe.questions);
      const largestWeightQuestion = allUpdatedQuestions.reduce(
        (max, q) => (q.weight > max.weight ? q : max),
        allUpdatedQuestions[0]
      );

      // Apply the tiny adjustment
      result = result.map((pdfEval) => ({
        ...pdfEval,
        questions: pdfEval.questions.map((q) => ({
          ...q,
          weight:
            q.id === largestWeightQuestion.id
              ? q.weight + diff // Add or subtract the tiny difference
              : q.weight,
        })),
        totalWeight: pdfEval.questions.reduce(
          (sum, q) =>
            sum +
            (q.id === largestWeightQuestion.id ? q.weight + diff : q.weight),
          0
        ),
      }));
    }

    return result;
  };

  const handleAddQuestion = (pdfId: string) => {
    const newQuestion: Question = {
      id: Math.random().toString(36).substr(2, 9),
      text: "",
      type: "binary",
      weight: 0, // Will be recalculated
    };

    let updatedEvaluations = evaluationSheet.pdfEvaluations.map((pdfEval) => {
      if (pdfEval.pdfId === pdfId) {
        return {
          ...pdfEval,
          questions: [...pdfEval.questions, newQuestion],
          totalWeight: 0, // Will be recalculated
        };
      }
      return pdfEval;
    });

    // Recalculate all weights to maintain total of 100
    updatedEvaluations = recalculateWeights(updatedEvaluations);

    onEvaluationSheetChange({
      pdfEvaluations: updatedEvaluations,
      totalWeight: 100,
    });
  };

  const handleDeleteQuestion = (pdfId: string, questionId: string) => {
    const pdfEval = evaluationSheet.pdfEvaluations.find(
      (pe) => pe.pdfId === pdfId
    );
    if (!pdfEval) return;

    const updatedEvaluations = evaluationSheet.pdfEvaluations.map((pdfEval) => {
      if (pdfEval.pdfId === pdfId) {
        return {
          ...pdfEval,
          questions: pdfEval.questions.filter((q) => q.id !== questionId),
          totalWeight: 0, // Will be recalculated
        };
      }
      return pdfEval;
    });

    // Recalculate all weights to maintain total of 100
    const recalculatedEvaluations = recalculateWeights(updatedEvaluations);

    onEvaluationSheetChange({
      pdfEvaluations: recalculatedEvaluations,
      totalWeight: 100,
    });
  };

  const handleQuestionChange = (
    pdfId: string,
    questionId: string,
    field: keyof Question,
    value: any
  ) => {
    let updatedEvaluations = evaluationSheet.pdfEvaluations.map((pdfEval) => {
      if (pdfEval.pdfId === pdfId) {
        return {
          ...pdfEval,
          questions: pdfEval.questions.map((q) => {
            if (q.id === questionId) {
              return { ...q, [field]: value };
            }
            return q;
          }),
        };
      }
      return pdfEval;
    });

    if (field === "weight") {
      // Get all questions across all PDFs
      const allQuestions = updatedEvaluations.flatMap((pe) => pe.questions);
      const changedQuestion = allQuestions.find((q) => q.id === questionId);
      if (!changedQuestion) return;

      const otherQuestions = allQuestions.filter((q) => q.id !== questionId);
      const newWeight = Math.max(0, Math.min(100, value)); // Clamp between 0 and 100
      const remainingWeight = 100 - newWeight;

      if (otherQuestions.length > 0) {
        // Distribute remaining weight proportionally among other questions
        const currentOtherTotal = otherQuestions.reduce(
          (sum, q) => sum + q.weight,
          0
        );
        const scaleFactor =
          currentOtherTotal > 0 ? remainingWeight / currentOtherTotal : 1;

        // First pass: Update all weights
        updatedEvaluations = updatedEvaluations.map((pdfEval) => ({
          ...pdfEval,
          questions: pdfEval.questions.map((q) => ({
            ...q,
            weight:
              q.id === questionId
                ? newWeight
                : Math.max(0, q.weight * scaleFactor),
          })),
        }));

        // Calculate total and adjust if needed
        const allUpdatedQuestions = updatedEvaluations.flatMap(
          (pe) => pe.questions
        );
        const totalAfterUpdate = allUpdatedQuestions.reduce(
          (sum, q) => sum + q.weight,
          0
        );
        const diff = 100 - totalAfterUpdate;

        if (Math.abs(diff) <= 0.1) {
          // If we're close but not quite at 100
          // Find the question with the largest weight to adjust
          const largestWeightQuestion = allUpdatedQuestions.reduce(
            (max, q) => (q.weight > max.weight ? q : max),
            allUpdatedQuestions[0]
          );

          // Final pass: Apply the tiny adjustment
          updatedEvaluations = updatedEvaluations.map((pdfEval) => ({
            ...pdfEval,
            questions: pdfEval.questions.map((q) => ({
              ...q,
              weight:
                q.id === largestWeightQuestion.id
                  ? q.weight + diff // Add or subtract the tiny difference
                  : q.weight,
            })),
            totalWeight: pdfEval.questions.reduce(
              (sum, q) =>
                sum +
                (q.id === largestWeightQuestion.id
                  ? q.weight + diff
                  : q.weight),
              0
            ),
          }));
        }
      }
    }

    // Calculate the total weight across all PDFs
    const totalWeight = updatedEvaluations.reduce(
      (sum, pdfEval) => sum + pdfEval.totalWeight,
      0
    );

    onEvaluationSheetChange({
      pdfEvaluations: updatedEvaluations,
      totalWeight,
    });
  };

  const totalWeightError = React.useMemo(() => {
    const total = evaluationSheet.pdfEvaluations.reduce(
      (sum, pdfEval) =>
        sum + pdfEval.questions.reduce((s, q) => s + q.weight, 0),
      0
    );

    if (
      evaluationSheet.pdfEvaluations.some((pdf) => pdf.questions.length > 0)
    ) {
      if (Math.abs(total - 100) > 0.01) {
        // Using small epsilon for floating point comparison
        return `Total weight must be 100%. Current total: ${total.toFixed(1)}%`;
      }
    }
    return "";
  }, [evaluationSheet]);

  React.useEffect(() => {
    // Update the parent's error state whenever weight validation changes
    if (onEvaluationSheetChange && totalWeightError) {
      onEvaluationSheetChange({
        ...evaluationSheet,
        errors: {
          ...evaluationSheet.errors,
          weightValidation: totalWeightError,
        },
      });
    }
  }, [totalWeightError, evaluationSheet]);

  return (
    <Box>
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: "flex", alignItems: "center", gap: 2, mb: 2 }}>
          <Typography variant="h6">
            Total Weight: {evaluationSheet.totalWeight}%
          </Typography>
          {totalWeightError ? (
            <Typography color="error" variant="subtitle1">
              {totalWeightError}
            </Typography>
          ) : (
            <Typography color="success.main" variant="subtitle1">
              (✓ Weights sum to 100%)
            </Typography>
          )}
        </Box>
        {errors.evaluationSheet && (
          <Typography color="error" sx={{ mb: 2 }}>
            {errors.evaluationSheet}
          </Typography>
        )}
      </Box>

      {pdfs.map((pdf) => {
        const pdfEval = evaluationSheet.pdfEvaluations.find(
          (pe) => pe.pdfId === pdf.id
        ) || {
          pdfId: pdf.id,
          questions: [],
          totalWeight: 0,
        };

        return (
          <Accordion
            key={pdf.id}
            sx={{
              mb: 2,
              background: "rgba(17, 34, 64, 0.95)",
              backdropFilter: "blur(10px)",
              borderRadius: "4px !important",
              "&:before": {
                display: "none",
              },
            }}
          >
            <AccordionSummary
              expandIcon={<ExpandMoreIcon />}
              sx={{
                borderBottom: `1px solid ${theme.palette.divider}`,
              }}
            >
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  gap: 2,
                  width: "100%",
                }}
              >
                <Typography variant="subtitle1">{pdf.title}</Typography>
                <Typography
                  variant="body2"
                  sx={{ color: theme.palette.primary.main }}
                >
                  {pdfEval.totalWeight}% of total
                </Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Box sx={{ display: "flex", justifyContent: "flex-end", mb: 2 }}>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={() => handleAddQuestion(pdf.id)}
                  sx={{
                    background: "linear-gradient(45deg, #64FFDA, #7B89F4)",
                    color: "#0A192F",
                    "&:hover": {
                      background: "linear-gradient(45deg, #5A6AD4, #A5B4FF)",
                    },
                  }}
                >
                  Add Question
                </Button>
              </Box>

              {pdfEval.questions.map((question, index) => (
                <Paper
                  key={question.id}
                  sx={{
                    p: 3,
                    mb: 2,
                    background: "rgba(17, 34, 64, 0.95)",
                    backdropFilter: "blur(10px)",
                    borderRadius: 2,
                    border: `1px solid ${theme.palette.divider}`,
                  }}
                >
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      mb: 2,
                    }}
                  >
                    <Typography variant="h6">Question {index + 1}</Typography>
                    <IconButton
                      onClick={() => handleDeleteQuestion(pdf.id, question.id)}
                      sx={{ color: theme.palette.error.main }}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Box>

                  <TextField
                    fullWidth
                    label="Requirement"
                    placeholder="Describe the requirement that needs to be implemented"
                    value={question.text}
                    onChange={(e) =>
                      handleQuestionChange(
                        pdf.id,
                        question.id,
                        "text",
                        e.target.value
                      )
                    }
                    sx={{ mb: 2 }}
                    helperText="Points will be awarded if requirement is met (evaluator clicks 'Yes')"
                  />

                  <Box sx={{ display: "flex", gap: 2, mb: 2 }}>
                    <TextField
                      label="Weight (%)"
                      type="number"
                      value={question.weight}
                      onChange={(e) =>
                        handleQuestionChange(
                          pdf.id,
                          question.id,
                          "weight",
                          parseInt(e.target.value) || 0
                        )
                      }
                      sx={{ width: "200px" }}
                      inputProps={{ min: 0, max: 100 }}
                      helperText={`Weights are automatically balanced to sum to 100%`}
                    />
                  </Box>
                </Paper>
              ))}
            </AccordionDetails>
          </Accordion>
        );
      })}
    </Box>
  );
};

export default EvaluationSheetManagement;
