import os
import logging
import requests
import jwt
import uuid
from datetime import datetime, timedelta
from django.conf import settings

logger = logging.getLogger(__name__)

def should_send_emails():
    """
    Check if emails should be sent based on environment.
    Returns False if DEV_ENV is 'development' and FORCE_SEND_EMAILS is not set.
    """
    dev_env = os.getenv('DEV_ENV', 'development')
    force_send = os.getenv('FORCE_SEND_EMAILS', 'false').lower() == 'true'
    return dev_env != 'development' or force_send

def send_brevo_email(to_emails, subject, html_content, text_content=None, attachments=None):
    """
    Send email using Brevo REST API
    Args:
        to_emails: List of email addresses
        subject: Email subject
        html_content: HTML content of the email
        text_content: Plain text content of the email
        attachments: List of dictionaries with 'url' and 'name' keys for attachments
    """
    # Skip sending emails in development environment
    if not should_send_emails():
        logger.info(f"Email sending skipped (development environment). Would have sent to: {to_emails}")
        logger.debug(f"Subject: {subject}")
        logger.debug(f"Content: {html_content}")
        return {"messageId": "dev-environment-skip"}

    try:
        headers = {
            "accept": "application/json",
            "api-key": os.getenv('EMAIL_HOST_PASSWORD'),
            "content-type": "application/json"
        }
        
        # Format recipients
        if isinstance(to_emails, str):
            to_emails = [to_emails]
            
        to = [{"email": email, "name": email.split('@')[0]} for email in to_emails]
        
        payload = {
            "sender": {
                "name": "Dev Space",
                "email": os.getenv('DEFAULT_FROM_EMAIL')
            },
            "to": to,
            "subject": subject,
            "htmlContent": html_content
        }
        
        if text_content:
            payload["textContent"] = text_content
            
        if attachments:
            payload["attachment"] = attachments

        response = requests.post(
            "https://api.brevo.com/v3/smtp/email",
            headers=headers,
            json=payload
        )
        
        if response.status_code == 201:
            message_id = response.json().get('messageId')
            logger.info(f"Email sent successfully via Brevo API. MessageId: {message_id}")
            return True
        else:
            logger.error(f"Failed to send email. Status code: {response.status_code}, Response: {response.text}")
            raise Exception(f"Failed to send email: {response.text}")
            
    except Exception as e:
        logger.error(f"Exception when calling Brevo API: {e}")
        raise

def send_registration_email(user):
    """Send registration confirmation email to user"""
    try:
        subject = 'Welcome to Dev Space - Confirm Your Registration'
        html_content = f'''
        <h1>Welcome to Dev Space!</h1>
        <p>Thank you for registering. Here are your registration details:</p>
        <ul>
            <li>Name: {user.first_name} {user.last_name}</li>
            <li>Role: {user.get_user_type_display()}</li>
        </ul>
        <p>Please follow these steps to complete your registration:</p>
        <ol>
            <li>Click the link below to confirm your email address:
                <p><a href="{os.getenv('FRONTEND_URL')}/verify-email/{user.approval_code}">
                    Confirm Registration
                </a></p>
            </li>
            <li>After confirming your email, please wait for admin approval. You will receive another email once your registration is approved.</li>
        </ol>
        <p><strong>Note:</strong> Your account will be fully activated only after both email verification and admin approval are complete.</p>
        <p>If you didn't create this account, please ignore this email.</p>
        '''
        
        return send_brevo_email(
            to_emails=user.email,
            subject=subject,
            html_content=html_content
        )
    except Exception as e:
        logger.error(f"Failed to send registration email to {user.email}: {str(e)}")
        raise

def send_admin_notification(user):
    """Notify admins about new user registration"""
    try:
        admin_emails = os.getenv('ADMIN_EMAILS', '').split(',')
        if not admin_emails or not admin_emails[0]:
            return False
            
        subject = 'New User Registration'
        html_content = f'''
        <h1>New User Registration</h1>
        <p>A new user has registered:</p>
        <ul>
            <li>Username: {user.username}</li>
            <li>Email: {user.email}</li>
            <li>Full Name: {user.first_name} {user.last_name}</li>
            <li>Role: {user.get_user_type_display()}</li>
        </ul>

        <p>Click the link below to approve the registration:</p>
        <p><a href="{os.getenv('FRONTEND_URL')}/admin-approve/{user.admin_approval_code}">
            Approve Registration
        </a></p>
        '''
        
        return send_brevo_email(
            to_emails=admin_emails,
            subject=subject,
            html_content=html_content
        )
    except Exception as e:
        logger.error(f"Failed to send admin notification for {user.email}: {str(e)}")
        raise

def send_approval_confirmation_email(user):
    """Send confirmation email to user when their account is approved by admin"""
    try:
        subject = 'Welcome to Dev Space - Your Account is Approved!'
        html_content = f'''
        <h1>Welcome to Dev Space! 🎉</h1>
        <p>Great news! Your account has been approved by our administrators.</p>
        
        <h2>Your Account Details:</h2>
        <ul>
            <li>Name: {user.first_name} {user.last_name}</li>
            <li>Role: {user.get_user_type_display()}</li>
        </ul>

        <p>Please click the button below to continue to Dev Space:</p>
        
        <p><a href="{os.getenv('FRONTEND_URL')}/pending-approval" style="display: inline-block; background-color: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; margin: 16px 0;">
            Continue to Dev Space
        </a></p>

        <p>We're excited to have you on board! If you have any questions, feel free to reach out to our support team.</p>
        
        <p>Best regards,<br>The Dev Space Team</p>
        '''
        
        return send_brevo_email(
            to_emails=user.email,
            subject=subject,
            html_content=html_content
        )
    except Exception as e:
        logger.error(f"Failed to send approval confirmation email to {user.email}: {str(e)}")
        raise

def password_reset_email(user, reset_link):
    """Send password reset email to user"""
    try:
        subject = 'Password Reset Request - Dev Space'
        html_content = f'''
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .button {{ background-color: #1976d2; color: white; padding: 12px 24px; 
                          text-decoration: none; border-radius: 4px; display: inline-block; margin: 20px 0; }}
                .warning {{ color: #666; font-size: 0.9em; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>Password Reset Request</h1>
                <p>Hello {user.first_name or user.email},</p>
                <p>We received a request to reset your password for your Dev Space account. 
                   If you didn't make this request, you can safely ignore this email.</p>
                <p><a href="{reset_link}" class="button">Reset Password</a></p>
                <p class="warning">This link will expire in 24 hours for security reasons.</p>
                <p class="warning">If the button above doesn't work, copy and paste this link into your browser:</p>
                <p class="warning">{reset_link}</p>
                <hr>
                <p style="font-size: 0.8em; color: #666;">
                    This is an automated message, please do not reply to this email.
                </p>
            </div>
        </body>
        </html>
        '''
        
        # Plain text version for email clients that prefer it
        text_content = f'''
        Password Reset Request - Dev Space

        Hello {user.first_name or user.email},

        We received a request to reset your password for your Dev Space account. 
        If you didn't make this request, you can safely ignore this email.

        To reset your password, click or copy this link:
        {reset_link}

        This link will expire in 24 hours for security reasons.

        This is an automated message, please do not reply to this email.
        '''
        
        return send_brevo_email(
            to_emails=user.email,
            subject=subject,
            html_content=html_content,
            text_content=text_content
        )
    except Exception as e:
        logger.error(f"Failed to send password reset email to {user.email}: {str(e)}")
        raise

def send_password_reset_email(user):
    try:
        # Generate reset link with enhanced security
        token = jwt.encode({
            'user_id': user.id,
            'exp': datetime.utcnow() + timedelta(hours=24),
            'iat': datetime.utcnow(),
            'jti': str(uuid.uuid4()),
            'type': 'password_reset'
        }, str(settings.SECRET_KEY), algorithm='HS256')
        
        frontend_url = os.getenv('FRONTEND_URL', 'http://localhost:3000')
        reset_link = f"{frontend_url}/reset-password/{token}"
        
        return password_reset_email(user, reset_link)
    except Exception as e:
        logger.error(f"Failed to send password reset email to {user.email}: {str(e)}")
        raise
