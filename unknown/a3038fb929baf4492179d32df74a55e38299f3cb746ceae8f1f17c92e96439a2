from django.core.management.base import BaseCommand
from users.models import Track, User
from projects.models import Project, Question, Course, CourseRegistrationRequest
from django.db import transaction
from django.contrib.auth.hashers import make_password
from django.core.files import File
import random

class Command(BaseCommand):
    help = 'Populate the database with initial tracks, courses, users, projects, questions, and course registrations'

    @transaction.atomic
    def handle(self, *args, **kwargs):

        # Create Tracks
        tracks_data = [
            {'name': 'AI for University Students'},
            {'name': 'Frontend'},
            {'name': 'Backend'},
        ]
        for track_data in tracks_data:
            Track.objects.get_or_create(**track_data)
        self.stdout.write(self.style.SUCCESS('Created tracks'))

        # Create Courses
        courses_data = [
            {'code': 'AI101', 'name': 'Intro to AI', 'description': 'AI basics', 'track': 'AI for University Students'},
            {'code': 'FE101', 'name': 'Frontend Basics', 'description': 'Frontend basics', 'track': 'Frontend'},
            {'code': 'BE101', 'name': 'Backend Basics', 'description': 'Backend basics', 'track': 'Backend'},
        ]
        courses = []
        for course_data in courses_data:
            track = Track.objects.get(name=course_data['track'])
            course, _ = Course.objects.get_or_create(
                code=course_data['code'],
                defaults={
                    'name': course_data['name'],
                    'description': course_data['description'],
                    'track': track
                }
            )
            courses.append(course)
        self.stdout.write(self.style.SUCCESS('Created courses'))

        # Create Users (students and educators)
        users = []

        # Create students
        for i in range(8):
            user, created = User.objects.get_or_create(
                username=f'user{i+1}',
                defaults={
                    'email': f'user{i+1}@example.com',
                    'password': make_password('123123'),
                    'first_name': f'Student{i+1}',
                    'last_name': f'User{i+1}',
                    'phone_number': f'+971{random.randint(100000000, 999999999)}',
                    'user_type': 'S',  # Student
                    'points': random.randint(1, 5),  # Give students some points to start
                    'exp': random.randint(0, 100),
                    'level': random.randint(1, 3),
                    'is_approved': True,
                    'is_email_verified': True
                }
            )

            if created:
                # Assign random courses to students
                enrolled_courses = random.sample(courses, k=random.randint(1, len(courses)))
                user.enrolled_courses.set(enrolled_courses)

                # Set a random current course from enrolled courses
                if enrolled_courses:
                    user.current_course = random.choice(enrolled_courses)
                    user.save()

            users.append(user)

        # Create educators
        for i in range(2):
            user, created = User.objects.get_or_create(
                username=f'educator{i+1}',
                defaults={
                    'email': f'educator{i+1}@example.com',
                    'password': make_password('123123'),
                    'first_name': f'Educator{i+1}',
                    'last_name': f'Teacher{i+1}',
                    'phone_number': f'+971{random.randint(100000000, 999999999)}',
                    'user_type': 'E',  # Educator
                    'points': 10,  # Educators get more points
                    'exp': random.randint(50, 200),
                    'level': random.randint(3, 8),
                    'is_approved': True,
                    'is_email_verified': True
                }
            )

            if created:
                # Assign all courses to educators (they can see all courses)
                user.enrolled_courses.set(courses)

            users.append(user)

        self.stdout.write(self.style.SUCCESS('Created 8 students and 2 educators with course assignments'))

        # Create Superuser
        admin_user, created = User.objects.get_or_create(
            username='admin',
            defaults={
                'email': '<EMAIL>',
                'password': make_password('admin'),
                'first_name': 'Admin',
                'last_name': 'User',
                'user_type': 'E',
                'points': 100,
                'exp': 1000,
                'level': 10,
                'is_approved': True,
                'is_email_verified': True,
                'is_staff': True,
                'is_superuser': True
            }
        )
        if created:
            # Assign all courses to admin
            admin_user.enrolled_courses.set(courses)
            self.stdout.write(self.style.SUCCESS('Created superuser'))
        else:
            self.stdout.write(self.style.WARNING('Superuser already exists'))

        # Create some course registration requests (pending)
        student_users = [u for u in users if u.user_type == 'S']
        if student_users:
            # Create a few pending registration requests
            for i in range(3):
                student = random.choice(student_users)
                available_courses = [c for c in courses if c not in student.enrolled_courses.all()]
                if available_courses:
                    course = random.choice(available_courses)
                    CourseRegistrationRequest.objects.get_or_create(
                        student=student,
                        course=course,
                        defaults={'status': 'pending'}
                    )
            self.stdout.write(self.style.SUCCESS('Created sample course registration requests'))

        # Create Projects and Questions for each course
        projects = []
        for course in courses:
            for j in range(3):  # Create 3 projects per course
                project, created = Project.objects.get_or_create(
                    title=f'{course.name} Project {j+1}',
                    course=course,
                    defaults={
                        'description': f'This is project {j+1} for {course.name}. Students will learn key concepts and apply them practically.',
                        'pdf_file': '',  # Empty for now - you can add real PDF files later
                        'points_required': random.randint(1, 3),  # Vary the points required
                        'passing_score': random.choice([60.0, 70.0, 80.0]),  # Different passing scores
                        'required_evaluators': random.randint(1, 2),  # 1-2 evaluators required
                    }
                )
                projects.append(project)

                # Create questions for the project (3-5 questions per project)
                num_questions = random.randint(3, 5)
                question_templates = [
                    "Does the code follow best practices and conventions?",
                    "Is the functionality implemented correctly?",
                    "Is the code well-documented and readable?",
                    "Does the solution meet all requirements?",
                    "Is the project structure organized properly?",
                    "Are error cases handled appropriately?",
                    "Is the user interface intuitive and functional?",
                    "Does the code demonstrate understanding of key concepts?"
                ]

                if created:  # Only create questions for new projects
                    selected_questions = random.sample(question_templates, num_questions)
                    for k, question_text in enumerate(selected_questions):
                        Question.objects.get_or_create(
                            project=project,
                            text=question_text,
                            defaults={
                                'weight': random.choice([1.0, 1.5, 2.0])  # Different weights for questions
                            }
                        )

        self.stdout.write(self.style.SUCCESS(f'Created {len(projects)} projects with questions for all courses'))

        # Create sample project submissions
        from projects.models import ProjectSubmission, Evaluation
        student_users = [u for u in users if u.user_type == 'S']
        # Note: Only students can evaluate projects according to the model validation
        evaluator_users = [u for u in users if u.user_type == 'S']  # Students evaluate each other

        submissions = []
        for student in student_users[:5]:  # First 5 students submit projects
            # Get projects from courses the student is enrolled in
            student_courses = student.enrolled_courses.all()
            available_projects = Project.objects.filter(course__in=student_courses)

            if available_projects.exists():
                # Submit 1-2 projects per student
                num_submissions = random.randint(1, min(2, available_projects.count()))
                selected_projects = random.sample(list(available_projects), num_submissions)

                for project in selected_projects:
                    # Check if student has enough points
                    if student.points >= project.points_required:
                        # Get potential evaluators (other students in the same course)
                        potential_evaluators = [u for u in evaluator_users if u != student and project.course in u.enrolled_courses.all()]
                        assigned_evaluator = random.choice(potential_evaluators) if potential_evaluators and random.choice([True, False]) else None

                        submission = ProjectSubmission.objects.create(
                            project=project,
                            submitted_by=student,
                            github_repo=f'https://github.com/{student.username}/{project.title.lower().replace(" ", "-")}',
                            submission_type='github',
                            status=random.choice(['pending', 'in_evaluation', 'in_evaluation', 'completed', 'failed']),  # More likely to be in_evaluation
                            assigned_evaluator=assigned_evaluator
                        )
                        submissions.append(submission)

                        # Deduct points from student (simulate submission cost)
                        student.points -= project.points_required
                        student.save()

        self.stdout.write(self.style.SUCCESS(f'Created {len(submissions)} project submissions'))

        # Create sample evaluations for submissions in evaluation status
        evaluations_created = 0
        for submission in submissions:
            if submission.status == 'in_evaluation':
                # Get potential evaluators (students in the same course, excluding the submitter)
                potential_evaluators = [
                    u for u in evaluator_users
                    if u != submission.submitted_by and
                    submission.project.course in u.enrolled_courses.all()
                ]

                if potential_evaluators:
                    # Create 1-2 evaluations per completed/failed submission
                    num_evaluations = min(
                        random.randint(1, submission.project.required_evaluators),
                        len(potential_evaluators)
                    )
                    selected_evaluators = random.sample(potential_evaluators, num_evaluations)

                    for evaluator in selected_evaluators:
                        # Calculate a realistic score (mix of passing and failing scores)
                        score = random.uniform(40, 95)  # Random score range

                        try:
                            Evaluation.objects.create(
                                submission=submission,
                                evaluator=evaluator,
                                comments=f"Good work on this project. {'Well done!' if score >= 70 else 'Needs improvement in some areas.'}",
                                is_approved=score >= submission.project.passing_score,
                                score=round(score, 2)
                            )
                            evaluations_created += 1

                            # Give points to evaluator
                            evaluator.points += 1
                            evaluator.save()
                        except Exception:
                            # Skip if evaluation creation fails (e.g., duplicate evaluation)
                            continue

        self.stdout.write(self.style.SUCCESS(f'Created {evaluations_created} evaluations'))

        # Final summary
        self.stdout.write(self.style.SUCCESS('=== DATA POPULATION COMPLETE ==='))
        self.stdout.write(self.style.SUCCESS(f'Created:'))
        self.stdout.write(self.style.SUCCESS(f'  - {len(tracks_data)} tracks'))
        self.stdout.write(self.style.SUCCESS(f'  - {len(courses)} courses'))
        self.stdout.write(self.style.SUCCESS(f'  - {len(users)} users (8 students + 2 educators)'))
        self.stdout.write(self.style.SUCCESS(f'  - 1 admin user'))
        self.stdout.write(self.style.SUCCESS(f'  - {len(projects)} projects'))
        self.stdout.write(self.style.SUCCESS(f'  - {len(submissions)} project submissions'))
        self.stdout.write(self.style.SUCCESS(f'  - {evaluations_created} evaluations'))
        self.stdout.write(self.style.SUCCESS(f'  - Sample course registration requests'))
        self.stdout.write(self.style.SUCCESS(''))
        self.stdout.write(self.style.SUCCESS('Login credentials:'))
        self.stdout.write(self.style.SUCCESS('  Admin: username=admin, password=admin'))
        self.stdout.write(self.style.SUCCESS('  Users: username=user1-user8, password=123123'))
        self.stdout.write(self.style.SUCCESS('  Educators: username=educator1-educator2, password=123123'))
