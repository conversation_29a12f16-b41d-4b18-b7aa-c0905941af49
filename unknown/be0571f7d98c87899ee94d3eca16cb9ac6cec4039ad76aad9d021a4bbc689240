import pytest
import shutil
from pathlib import Path
from django.conf import settings

@pytest.fixture(autouse=True)
def setup_test_environment(tmp_path, django_db_setup):
    """Set up test environment with local file storage."""
    # Ensure we're using FileSystemStorage for tests
    settings.USE_SPACES = False
    
    # Create test media directory
    media_test_dir = Path(settings.MEDIA_ROOT)
    media_test_dir.mkdir(parents=True, exist_ok=True)
    
    yield
    
    # Cleanup test media files after test
    if media_test_dir.exists():
        shutil.rmtree(media_test_dir)
