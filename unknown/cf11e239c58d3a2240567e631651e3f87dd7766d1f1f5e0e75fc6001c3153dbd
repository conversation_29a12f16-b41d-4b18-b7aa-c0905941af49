name: Run Tests

on:
  pull_request:
    branches:
      - dev
  push:
    branches:
      - dev

jobs:
  test:
    runs-on: ubuntu-latest

    env:
      # Core Django settings
      DJANGO_SECRET_KEY: django-insecure-test-key-123
      DEBUG: "True"

      # Database settings - using actual values
      POSTGRES_DB: web_dev_club
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_HOST: db
      POSTGRES_PORT: 5432

      # Application settings - using actual values
      ADMIN_EMAILS: <EMAIL>
      FRONTEND_URL: http://localhost:3000
      DEV_ENV: development

      # Force local storage for tests
      USE_SPACES: "False"

    steps:
      - uses: actions/checkout@v3

      - name: Create test environment file
        run: |
          cat << EOF > backend/.env
          DJANGO_SECRET_KEY=${{ env.DJANGO_SECRET_KEY }}
          DEBUG=${{ env.DEBUG }}
          POSTGRES_DB=${{ env.POSTGRES_DB }}
          POSTGRES_USER=${{ env.POSTGRES_USER }}
          POSTGRES_PASSWORD=${{ env.POSTGRES_PASSWORD }}
          POSTGRES_HOST=${{ env.POSTGRES_HOST }}
          POSTGRES_PORT=${{ env.POSTGRES_PORT }}
          USE_SPACES=False
          SPACES_BUCKET_NAME=$SPACES_BUCKET_NAME
          SPACES_REGION=$SPACES_REGION
          USE_SPACES=True
          EMAIL_HOST_PASSWORD=test-password
          DEFAULT_FROM_EMAIL=<EMAIL>
          ADMIN_EMAILS=${{ env.ADMIN_EMAILS }}
          FRONTEND_URL=${{ env.FRONTEND_URL }}
          DEV_ENV=${{ env.DEV_ENV }}
          EOF

          # Debug: Print environment file without sensitive values
          echo "Created .env file with the following structure:"
          cat backend/.env | grep -vE "KEY|PASSWORD"

      - name: Set up Docker Compose
        env:
          SPACES_ACCESS_KEY: ${{ secrets.SPACES_ACCESS_KEY }}
          SPACES_SECRET_KEY: ${{ secrets.SPACES_SECRET_KEY }}
          SPACES_BUCKET_NAME: ${{ secrets.SPACES_BUCKET_NAME }}
          SPACES_REGION: ${{ secrets.SPACES_REGION }}
        run: |
          docker compose -f backend/docker-compose.yml build
          docker compose -f backend/docker-compose.yml up -d

      - name: Wait for services to be ready
        run: |
          # Wait for PostgreSQL to be ready
          docker exec backend-db-1 /bin/sh -c 'until pg_isready; do sleep 1; done'

      - name: Run migrations
        run: docker exec backend-backend-1 python manage.py migrate

      - name: Run tests
        run: docker exec backend-backend-1 pytest -v --color=yes

      - name: Clean up
        if: always()
        run: |
          docker exec backend-backend-1 python manage.py cleanup_test_files
          docker compose -f backend/docker-compose.yml down
          rm backend/.env
