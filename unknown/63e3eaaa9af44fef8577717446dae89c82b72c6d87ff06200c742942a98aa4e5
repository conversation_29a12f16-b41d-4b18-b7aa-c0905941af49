import React, { useState, useEffect } from "react";
import {
  Grid,
  Paper,
  Typography,
  Box,
  Tabs,
  Tab,
  CircularProgress,
  Card,
  CardContent,
  Chip,
  Button,
  Link,
  Divider,
  LinearProgress,
} from "@mui/material";
import { Project, ProjectSubmission } from "@/types";
import api from "@/services/api";
import ProjectView from "@/components/ProjectView";
import { useAuth } from "@/contexts/AuthContext";
import DescriptionIcon from '@mui/icons-material/Description';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import PendingIcon from '@mui/icons-material/Pending';
import FolderIcon from '@mui/icons-material/Folder';
import LinkIcon from '@mui/icons-material/Link';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;
  return (
    <div role="tabpanel" hidden={value !== index} {...other}>
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
};

const Projects: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [projects, setProjects] = useState<Project[]>([]);
  const [mySubmissions, setMySubmissions] = useState<ProjectSubmission[]>([]);
  const [tabValue, setTabValue] = useState(0);
  const { fetchUser } = useAuth();

  const getScoreColor = (score: number) => {
    if (score >= 90) return "success.main";
    if (score >= 70) return "success.light";
    return "error.main";
  };

  const getBorderColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success.main';
      case 'failed':
        return 'error.main';
      case 'pending':
        return 'warning.main';
      case 'in_evaluation':
        return 'warning.main';  // Changed to warning (yellow) for in_evaluation
      default:
        return 'grey.300';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "warning";
      case "in_evaluation":
        return "warning";  // Changed to warning (yellow) for in_evaluation
      case "completed":
        return "success";
      case "failed":
        return "error";
      default:
        return "default";
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const StatusCard = ({ submission }: { submission: ProjectSubmission }) => {
    return (
      <Card sx={{
        width: '100%',
        mt: 2,
        borderLeft: 6,
        borderColor: getBorderColor(submission.status)
      }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            {submission.evaluations && submission.evaluations.length > 0 ? (
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                {/* Show individual evaluator scores */}
                {submission.evaluations.map((evaluation) => (
                  <Typography key={evaluation.id} variant="body2" color="text.secondary">
                    {evaluation.evaluator.first_name} {evaluation.evaluator.last_name}: <span style={{ color: getScoreColor(evaluation.score || 0) }}>{Math.round(evaluation.score || 0)}%</span>
                  </Typography>
                ))}
                
                {/* Always show current average score */}
                {submission.final_score !== null && (
                  <Box sx={{ mt: 1 }}>
                    <Typography variant="h6" color={getScoreColor(submission.final_score)}>
                      {submission.evaluation_progress.current === submission.evaluation_progress.required ? 'Final' : 'Current'} Score: {Math.round(submission.final_score)}%
                    </Typography>
                    {submission.evaluation_progress.current < submission.evaluation_progress.required && (
                      <Typography variant="body2" color="text.secondary">
                        Waiting for {submission.evaluation_progress.required - submission.evaluation_progress.current} more {submission.evaluation_progress.required - submission.evaluation_progress.current === 1 ? 'evaluation' : 'evaluations'}
                      </Typography>
                    )}
                  </Box>
                )}
              </Box>
            ) : null}
            <Chip
              label={submission.status.toUpperCase()}
              color={getStatusColor(submission.status)}
              sx={{ px: 2 }}
            />
          </Box>

          {/* Always show evaluation progress if there are any evaluations */}
          {submission.evaluation_progress && (
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, color: submission.status === 'pending' ? 'warning.main' : submission.status === 'completed' ? 'success.main' : 'info.main' }}>
                {submission.evaluation_progress.current === submission.evaluation_progress.required ? (
                  <CheckCircleOutlineIcon />
                ) : submission.evaluation_progress.current === 0 ? (
                  <PendingIcon />
                ) : (
                  <AccessTimeIcon />
                )}
                <Typography>
                  {submission.evaluation_progress.current === 0 ? 'Awaiting Review' :
                   submission.evaluation_progress.current < submission.evaluation_progress.required ? 
                   `${submission.evaluation_progress.current} out of ${submission.evaluation_progress.required} Evaluations Received` :
                   'All Evaluations Received'}
                </Typography>
              </Box>
              {submission.evaluation_progress.current > 0 && (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Typography variant="body2" color="text.secondary">
                    Progress: {submission.evaluation_progress.current}/{submission.evaluation_progress.required} evaluations
                  </Typography>
                  <Box sx={{ flexGrow: 1, ml: 1 }}>
                    <LinearProgress 
                      variant="determinate" 
                      value={(submission.evaluation_progress.current * 100) / submission.evaluation_progress.required}
                      color="warning"
                    />
                  </Box>
                </Box>
              )}
            </Box>
          )}

          {submission.evaluations && submission.evaluations.length > 0 && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle2" sx={{ mb: 1, color: 'text.secondary' }}>
                Evaluator Feedback:
              </Typography>
              {submission.evaluations.map((evaluation, index) => (
                <Paper key={evaluation.id} sx={{ p: 2, bgcolor: 'background.default', mb: 2 }}>
                  <Typography variant="subtitle2" color="primary" sx={{ mb: 1 }}>
                    Evaluator {index + 1}:
                  </Typography>
                  <Typography sx={{ fontStyle: 'italic', color: 'text.secondary' }}>
                    "{evaluation.comments}"
                  </Typography>
                </Paper>
              ))}
              {submission.status === 'failed' && submission.evaluation_progress.is_complete && (
                <Typography color="error" sx={{ mt: 2 }}>
                  You can submit this project again after gaining a point.
                </Typography>
              )}
            </Box>
          )}
        </CardContent>
      </Card>
    );
  };

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [, submissionsRes] = await Promise.all([
        fetchUser(),
        api.get<ProjectSubmission[]>("/projects/my-submissions/"),
      ]);

      // Get user's enrolled courses
      const coursesRes = await api.get("/projects/courses/");
      const enrolledCourses = coursesRes.data;

      // Fetch projects from all enrolled courses
      const allProjects: Project[] = [];
      for (const course of enrolledCourses) {
        try {
          const courseProjectsRes = await api.get<Project[]>(`/projects/courses/${course.code}/projects/`);
          allProjects.push(...courseProjectsRes.data);
        } catch (error) {
          console.error(`Error fetching projects for course ${course.code}:`, error);
        }
      }

      setProjects(allProjects);
      setMySubmissions(submissionsRes.data);
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  if (loading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="50vh"
      >
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Paper sx={{ width: "100%", mb: 2 }}>
      <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
        <Tabs value={tabValue} onChange={handleTabChange}>
          <Tab label="Projects List" />
          <Tab label="My Submissions" />
        </Tabs>
      </Box>

      <TabPanel value={tabValue} index={0}>
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" color="text.secondary">
            Projects List
          </Typography>
          <Typography variant="body2" color="text.secondary">
            All projects in your track are shown below. Projects with a green level and points chip can be submitted.
          </Typography>
        </Box>
        <Grid container spacing={3}>
          {projects.map((project) => {
            const submission = mySubmissions.find(
              (s) => s.project.id === project.id
            );
            return (
              <Grid item xs={12} key={project.id}>
                <Paper sx={{ p: 3, borderLeft: 6, borderColor: project.can_submit ? 'success.main' : 'warning.main' }}>
                  <ProjectView
                    project={project}
                    onSubmit={fetchData}
                    submissionStatus={submission?.status}
                    onSubmitSuccess={fetchData}
                  />
                  {!project.can_submit && (
                    <Typography variant="body2" color="warning.main" sx={{ mt: 2 }}>
                      Complete lower level projects first to unlock this project.
                    </Typography>
                  )}
                </Paper>
              </Grid>
            );
          })}
          {projects.length === 0 && (
            <Grid item xs={12}>
              <Typography variant="body1" color="text.secondary" align="center">
                No projects available in your track.
              </Typography>
            </Grid>
          )}
        </Grid>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <Grid container spacing={3}>
          {mySubmissions.map((submission) => (
            <Grid item xs={12} key={submission.id}>
              <Card sx={{ 
                '&:hover': { 
                  boxShadow: 6,
                  transition: 'box-shadow 0.3s ease-in-out'
                }
              }}>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                    <Typography variant="h5" color="primary.main" sx={{ fontWeight: 500 }}>
                      {submission.project.title}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Submitted {formatDate(submission.created_at)}
                    </Typography>
                  </Box>
                  
                  <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                    <Chip
                      label={`Level ${submission.project.level_required}`}
                      size="small"
                      sx={{ bgcolor: 'primary.light', color: 'primary.dark' }}
                    />
                    <Chip
                      label={`${submission.project.points_required} Points`}
                      size="small"
                      sx={{ bgcolor: 'secondary.light', color: 'secondary.dark' }}
                    />
                  </Box>

                  <StatusCard submission={submission} />

                  <Divider sx={{ my: 2 }} />

                  <Box sx={{ display: "flex", gap: 2, flexDirection: 'column', mt: 3 }}>
                    <Box sx={{ display: "flex", gap: 2, mb: 2 }}>
                      <Button
                        variant="outlined"
                        startIcon={<DescriptionIcon />}
                        component={Link}
                        href={submission.project.pdf_file || ''}
                        target="_blank"
                      >
                        View Requirements
                      </Button>
                    </Box>
                    
                    {submission.submission_type === 'github' && submission.github_repo && (
                      <Box>
                        <Typography variant="subtitle1" gutterBottom>Project URL:</Typography>
                        <Button
                          variant="contained"
                          component={Link}
                          href={submission.github_repo || ''}
                          target="_blank"
                          startIcon={<LinkIcon />}
                          sx={{
                            background: 'linear-gradient(45deg, #64FFDA, #7B89F4)',
                            '&:hover': {
                              background: 'linear-gradient(45deg, #5A6AD4, #A5B4FF)',
                            },
                            textTransform: 'none',
                            fontSize: '1rem',
                            py: 1.5,
                            px: 4,
                            minWidth: '200px'
                          }}
                        >
                          View Project
                        </Button>
                      </Box>
                    )}
                    
                    {submission.submission_type === 'zip' && submission.zip_file && (
                      <Box>
                        <Typography variant="subtitle1" gutterBottom>Submitted Folder:</Typography>
                        <Paper
                          elevation={3}
                          sx={{
                            p: 3,
                            cursor: 'pointer',
                            border: theme => `2px solid ${theme.palette.primary.main}`,
                            transition: 'all 0.2s',
                            '&:hover': {
                              transform: 'translateY(-2px)',
                              boxShadow: theme => theme.shadows[6],
                            },
                            maxWidth: '500px'
                          }}
                        >
                          <Link 
                            href={submission.zip_file || ''}
                            target="_blank"
                            download
                            sx={{ textDecoration: 'none' }}
                          >
                            <Box
                              display="flex"
                              flexDirection="column"
                              alignItems="center"
                              gap={2}
                              sx={{
                                minHeight: '120px',
                                justifyContent: 'center',
                                borderRadius: 1,
                                p: 2,
                              }}
                            >
                              <FolderIcon sx={{ 
                                fontSize: 40,
                                color: theme => theme.palette.primary.main
                              }} />
                              <Typography 
                                variant="h6" 
                                align="center"
                                sx={{ 
                                  color: 'text.primary',
                                  fontWeight: 500
                                }}
                              >
                                Download Project Files
                              </Typography>
                              <Typography 
                                variant="body2" 
                                align="center"
                                sx={{ color: 'text.secondary' }}
                              >
                                Click to download the ZIP file
                              </Typography>
                            </Box>
                          </Link>
                        </Paper>
                      </Box>
                    )}
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
          {mySubmissions.length === 0 && (
            <Grid item xs={12}>
              <Typography variant="body1" color="text.secondary" align="center">
                You haven't submitted any projects yet.
              </Typography>
            </Grid>
          )}
        </Grid>
      </TabPanel>
    </Paper>
  );
};

export default Projects;
