from django.urls import path
from . import views

app_name = 'users'

urlpatterns = [
    path('register/', views.RegisterView.as_view(), name='register'),
    path('login/', views.UserLoginView.as_view(), name='login'),
    path('logout/', views.UserLogoutView.as_view(), name='logout'),
    path('profile/', views.UserProfileView.as_view(), name='user-profile'),
    path('tracks/', views.TrackListView.as_view(), name='track-list'),
    path('verify-email/<str:code>/', views.UserApprovalView.as_view(), name='verify-email'),
    path('admin-approve/<str:code>/', views.AdminApprovalView.as_view(), name='admin-approve'),
    path('forgot-password/', views.ForgotPasswordView.as_view(), name='forgot-password'),
    path('reset-password/<str:token>/', views.ResetPasswordView.as_view(), name='reset-password'),
]
