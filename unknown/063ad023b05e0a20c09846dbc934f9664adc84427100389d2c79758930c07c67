import React from 'react';
import { Box, Typography } from '@mui/material';

interface SectionTitleProps {
  title: string;
}

const SectionTitle: React.FC<SectionTitleProps> = ({ title }) => {
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        mb: 6,
        position: 'relative',
      }}
    >
      <Typography
        variant="h3"
        sx={{
          position: 'relative',
          '&::after': {
            content: '""',
            position: 'absolute',
            bottom: '-10px',
            left: 0,
            width: '100%',
            height: '2px',
            background: 'linear-gradient(90deg, #64FFDA 0%, rgba(123, 137, 244, 0.1) 100%)',
          }
        }}
      >
        {title}
      </Typography>
    </Box>
  );
};

export default SectionTitle; 