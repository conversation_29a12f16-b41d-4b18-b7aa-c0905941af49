{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block content %}
<div>
    <h2>Send Bulk Emails</h2>
    
    {% if messages %}
    <ul class="messagelist">
        {% for message in messages %}
        <li{% if message.tags %} class="{{ message.tags }}"{% endif %}>{{ message }}</li>
        {% endfor %}
    </ul>
    {% endif %}

    <form method="post" enctype="multipart/form-data">
        {% csrf_token %}
        <div style="margin-bottom: 20px;">
            <label for="excel_file">Excel File:</label>
            <input type="file" name="excel_file" id="excel_file" accept=".xlsx" required>
        </div>
        
        <div style="margin-bottom: 20px;">
            <label for="subject">Email Subject:</label>
            <input type="text" name="subject" id="subject" required style="width: 100%; max-width: 500px;">
        </div>

        <div style="margin-bottom: 20px;">
            <label for="html_content">Email Content (HTML):</label>
            <div style="margin-bottom: 10px;">
                <button type="button" onclick="loadDefaultTemplate()" style="padding: 5px 10px;">Load Default Template</button>
            </div>
            <textarea name="html_content" id="html_content" required style="width: 100%; max-width: 800px; height: 400px; font-family: monospace;"></textarea>
        </div>

        <input type="submit" value="Send Emails" class="default" style="float: none">
    </form>
    
    <div style="margin-top: 20px;">
        <h3>Note:</h3>
        <p>The Excel file should contain the following columns:</p>
        <ul>
            <li>email (required) - The recipient's email address</li>
            <li>first_name (required) - The recipient's first name</li>
            <li>last_name (required) - The recipient's last name</li>
        </ul>
        <p>Available template variables:</p>
        <ul>
            <li>{first_name} - Will be replaced with the recipient's first name</li>
            <li>{last_name} - Will be replaced with the recipient's last name</li>
            <li>{email} - Will be replaced with the recipient's email</li>
        </ul>
    </div>
</div>

<script>
function loadDefaultTemplate() {
    const defaultTemplate = `
<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa; border-radius: 10px;">
    <div style="background: linear-gradient(135deg, #0066ff, #00ccff); padding: 30px; border-radius: 10px 10px 0 0; text-align: center;">
        <h1 style="color: white; margin: 0; font-size: 28px;">🚀 Welcome to DevSpace! 🌟</h1>
    </div>
    
    <div style="background: white; padding: 30px; border-radius: 0 0 10px 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <p style="font-size: 18px; color: #333;">Hey {first_name} {last_name}! 👋</p>
        
        <p style="color: #555; line-height: 1.6;">
            We're thrilled to have you join our community of passionate developers! 🎉
            Get ready to embark on an exciting journey of learning and growth. 💡
        </p>
        
        <div style="background: #f0f7ff; padding: 20px; border-radius: 8px; margin: 25px 0; border-left: 4px solid #0066ff;">
            <h3 style="color: #0066ff; margin-top: 0;">🔐 Your Login Credentials</h3>
            <p style="margin: 10px 0;"><strong>🌐 Website:</strong> <a href="https://42devspace.tech" style="color: #0066ff; text-decoration: none;">https://42devspace.tech</a></p>
            <p style="margin: 10px 0;"><strong>📧 Email:</strong> <span style="color: #444;">{email}</span></p>
            <p style="margin: 10px 0;"><strong>🔑 Password:</strong> <span style="color: #444;">42Devspace@ATA</span></p>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="https://42devspace.tech" style="background: #0066ff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 25px; font-weight: bold; display: inline-block; transition: background 0.3s;">
                🚀 Start Your Journey
            </a>
        </div>
        
        <div style="border-top: 2px solid #eee; margin-top: 30px; padding-top: 20px;">
            <p style="color: #666; margin-bottom: 5px;">What's next? 📝</p>
            <ul style="color: #666; padding-left: 20px;">
                <li>Log in to your account 🔐</li>
                <li>Complete your profile ✨</li>
                <li>Join our community 👥</li>
                <li>Start learning and growing! 📚</li>
            </ul>
        </div>
        
        <p style="color: #666; margin-top: 30px; text-align: center;">
            Need help? We're here for you! 💪<br>
            Happy coding! 💻
        </p>
        
        <div style="text-align: center; margin-top: 30px; color: #888;">
            <p style="margin: 5px 0;">Best regards,<br>The DevSpace Team ⭐</p>
        </div>
    </div>
</div>`;
    document.getElementById('html_content').value = defaultTemplate;
}

// Load default template when page loads
document.addEventListener('DOMContentLoaded', loadDefaultTemplate);
</script>
{% endblock %}
