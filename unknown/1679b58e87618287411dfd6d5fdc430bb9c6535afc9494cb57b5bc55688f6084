import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Avatar,
  Grid,
  IconButton,
  Badge,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  CircularProgress,
  LinearProgress,
  TextField,
  Tooltip,
  Divider,
  Alert,
} from '@mui/material';
import {
  useAuth
} from '../../contexts/AuthContext';
import EmojiEventsIcon from '@mui/icons-material/EmojiEvents';
import StarIcon from '@mui/icons-material/Star';
import AssignmentIcon from '@mui/icons-material/Assignment';
import CameraAltIcon from '@mui/icons-material/CameraAlt';
import api from '../../services/api';
import EditIcon from '@mui/icons-material/Edit';
import CheckIcon from '@mui/icons-material/Check';
import CloseIcon from '@mui/icons-material/Close';
import EmailIcon from '@mui/icons-material/Email';
import PhoneIcon from '@mui/icons-material/Phone';
import { User } from '@/types';

const Profile: React.FC = () => {
  const { user, setUser, checkAuth } = useAuth();
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [openDialog, setOpenDialog] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | undefined>(undefined);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const abortControllerRef = useRef<AbortController | null>(null);
  const [isEditingUsername, setIsEditingUsername] = useState(false);
  const [newUsername, setNewUsername] = useState(user?.username || '');
  const [usernameError, setUsernameError] = useState<string | null>(null);
  const [isEditingPhone, setIsEditingPhone] = useState(false);
  const [newPhone, setNewPhone] = useState('');
  const [phoneError, setPhoneError] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const MAX_FILE_SIZE = 2 * 1024 * 1024; // 2MB

  useEffect(() => {
    if (user) {
      setNewPhone(user.phone_number || '');
      setNewUsername(user.username || '');
    }
    return () => {};
  }, [user]);

  if (!user) return null;

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (!file.type.startsWith('image/')) {
        setError('Please select a valid image file');
        setOpenDialog(true);
        return;
      }
      if (file.size > MAX_FILE_SIZE) {
        setError('The file is too large. Please ensure your image is under 2MB.');
        setOpenDialog(true);
        return;
      }
      setError(null);
      setPreviewUrl(URL.createObjectURL(file));
      setOpenDialog(true);
    }
  };

  const handleUpload = async () => {
    const file = fileInputRef.current?.files?.[0];
    if (!file) return;

    setUploading(true);
    setUploadProgress(0);

    // Create a new AbortController for this upload
    abortControllerRef.current = new AbortController();
    const { signal } = abortControllerRef.current;

    try {
      const formData = new FormData();
      formData.append('profile_picture', file);

      // Make sure we're not aborted before starting
      if (signal.aborted) {
        throw new DOMException('Aborted', 'AbortError');
      }

      await api.patch('/users/profile/', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        signal, // Pass the signal to axios
        onUploadProgress: (progressEvent) => {
          // Check for abort during upload
          if (signal.aborted) {
            throw new DOMException('Aborted', 'AbortError');
          }
          
          // Update progress
          const progress = Math.round((progressEvent.loaded * 100) / (progressEvent.total || 1));
          setUploadProgress(progress);
        },
      });

      // Only update if not aborted
      if (!signal.aborted) {
        await checkAuth();
        setOpenDialog(false);
        if (previewUrl) {
          URL.revokeObjectURL(previewUrl);
          setPreviewUrl(undefined);
        }
      }
    } catch (error: any) {
      if (error.name === 'AbortError') {
        setError('Upload cancelled by user');
      } else if (error.response?.data?.error) {
        setError(error.response.data.error);
      } else {
        setError('Failed to upload image. Please try again.');
      }
    } finally {
      setUploading(false);
      setUploadProgress(0);
      abortControllerRef.current = null;
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleCloseDialog = () => {
    // If we're uploading, abort it immediately
    if (uploading && abortControllerRef.current) {
      abortControllerRef.current.abort();
      setUploading(false); // Reset upload state immediately
    }

    // Clean up all states
    setOpenDialog(false);
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl); // Clean up the object URL
      setPreviewUrl(undefined);
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    
    // Make sure to null out the abort controller
    abortControllerRef.current = null;
    
    // Clear error after dialog is closed
    setTimeout(() => setError(null), 100);
  };

  const handleDeleteProfilePicture = async () => {
    try {
      await api.patch('/users/profile/', { profile_picture: '' });
      await checkAuth();
    } catch (error) {
      console.error('Failed to delete profile picture', error);
    }
  };

  const handleUsernameUpdate = async () => {
    if (!newUsername.trim() || newUsername === user?.username) {
      setIsEditingUsername(false);
      setNewUsername(user?.username || '');
      return;
    }

    try {
      const formData = new FormData();
      formData.append('username', newUsername);

      await api.patch('/users/profile/', formData);
      await checkAuth();
      setIsEditingUsername(false);
      setUsernameError(null);
    } catch (error: any) {
      setUsernameError(error.response?.data?.error || 'Failed to update username');
    }
  };

  const fetchUser = async () => {
    const userRes = await api.get<User>('/users/profile/');
    setUser(userRes.data);
  };

  useEffect(() => {
    fetchUser();
  }, []);

  const validatePhoneNumber = (phone: string): boolean => {
    const phoneRegex = /^\+971\d{9}$/;
    return phoneRegex.test(phone);
  };

  return (
    <>
      <Box
        sx={{
          position: 'fixed',
          minHeight: '100vh',
          width: '100vw',
          backgroundAttachment: 'fixed',
          overflowY: 'auto',
          backgroundSize: 'cover',
          top: 20,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 1,
          p: { xs: 2, md: 4 },
        }}
      >
        <Grid container spacing={4} sx={{ maxWidth: '1400px', margin: '0 auto' }}>
          {/* Profile Overview */}
          <Grid item xs={12} md={4} sx={{ display: 'flex' }}>
            <Paper
              elevation={0}
              sx={{
                p: 4,
                mr: 10,
                ml: 1,
                background: 'linear-gradient(135deg, rgba(100, 255, 218, 0.15), rgba(123, 137, 244, 0.15))',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(100, 255, 218, 0.2)',
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                gap: 3,
                borderRadius: 4,
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                '&:hover': {
                  transform: 'translateY(-5px)',
                  boxShadow: '0 8px 24px rgba(100, 255, 218, 0.1)',
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    inset: 0,
                    borderRadius: 'inherit',
                    border: '1px solid rgba(100, 255, 218, 0.3)',
                  }
                }
              }}
            >
              <Box sx={{ 
                position: 'relative', 
                display: 'inline-block',
                mb: 3,
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  inset: -4,
                  background: 'linear-gradient(135deg, #64ffda, #7b89f4)',
                  borderRadius: '50%',
                  opacity: 0.2,
                  animation: 'pulse 2s infinite'
                }
              }}>
                <Box sx={{ position: 'relative' }}>

                  {user?.profile_picture && (
                    <Tooltip title="Remove profile picture" placement="top">
                      <IconButton
                        size="small"
                        onClick={handleDeleteProfilePicture}
                        sx={{
                          position: 'absolute',
                          top: 4,
                          right: 4,
                          bgcolor: 'rgba(0, 0, 0, 0.5)',
                          backdropFilter: 'blur(4px)',
                          color: 'white',
                          padding: '4px',
                          '&:hover': {
                            bgcolor: 'rgba(0, 0, 0, 0.7)',
                          },
                          width: 28,
                          height: 28,
                          minWidth: 28,
                          zIndex: 1,
                          transition: 'all 0.2s ease-in-out',
                          border: '1px solid rgba(255, 255, 255, 0.2)',
                        }}
                      >
                        <CloseIcon sx={{ fontSize: 18 }} />
                      </IconButton>
                    </Tooltip>
                  )}
                  <Badge
                    overlap="circular"
                    anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                    badgeContent={
                      <IconButton
                        size="small"
                        sx={{
                          bgcolor: 'primary.main',
                          '&:hover': { bgcolor: 'primary.dark' },
                        }}
                        component="label"
                      >
                        <input
                          hidden
                          accept="image/*"
                          type="file"
                          ref={fileInputRef}
                          onChange={handleFileSelect}
                        />
                        <CameraAltIcon sx={{ fontSize: 20, color: 'background.default' }} />
                      </IconButton>
                    }
                  >
                  <Avatar
                    src={user?.profile_picture || undefined}
                    sx={{ 
                      width: 180, 
                      height: 180,
                      border: '4px solid',
                      borderColor: 'rgba(100, 255, 218, 0.2)',
                      boxShadow: '0 0 16px rgba(100, 255, 218, 0.2)',
                      '&::after': {
                        content: '""',
                        position: 'absolute',
                        inset: 0,
                        borderRadius: '50%',
                        border: '2px solid rgba(100, 255, 218, 0.3)',
                        animation: 'pulse 2s infinite',
                      }
                    }}
                  />
                </Badge>
              </Box>
            </Box>

            {isEditingUsername ? (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <TextField
                  value={newUsername}
                  onChange={(e) => {
                    const input = e.target.value;
                    if (input.length <= 20) { // Allow only up to 20 characters
                      setNewUsername(input);
                      setUsernameError(null); // Clear error if input is valid
                    } else {
                      setUsernameError('Username cannot exceed 20 characters.');
                    }
                  }}
                  error={Boolean(usernameError)}
                  helperText={usernameError}
                  size="small"
                  sx={{ width: '200px' }}
                />
                <IconButton size="small" color="primary" onClick={handleUsernameUpdate}>
                  <CheckIcon />
                </IconButton>
                <IconButton 
                  size="small" 
                  color="error"
                  onClick={() => {
                    setIsEditingUsername(false);
                    setNewUsername(user?.username || '');
                    setUsernameError(null);
                  }}
                >
                  <CloseIcon />
                </IconButton>
              </Box>
            ) : (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Typography
                  variant="h4"
                  sx={{
                    fontSize: newUsername.length > 15 ? '1.5rem' : '2rem', // Adjust font size based on length
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    maxWidth: '100%',
                  }}
                >
                  {user?.username}
                </Typography>
                <Tooltip title="Edit username">
                  <IconButton size="small" onClick={() => setIsEditingUsername(true)}>
                    <EditIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
            )}

            <Box mt={2}>
              <Typography variant="h6">{user.first_name} {user.last_name}</Typography>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <EmailIcon sx={{ color: 'text.secondary' }} />
              <Typography variant="body1" color="text.secondary">
                {user?.email}
              </Typography>
            </Box>

            {isEditingPhone ? (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 2 }}>
                <TextField
                  value={newPhone.replace(/^\+971/, '')} // Remove +971 from the state value for display
                  onChange={(e) => {
                    const input = e.target.value;
                    if (/^\d{0,9}$/.test(input)) { // Allow only up to 9 digits
                      setNewPhone(`+971${input}`);
                      setPhoneError(null); // Clear error if input is valid
                    } else {
                      setPhoneError('Phone number must be 9 digits after +971.');
                    }
                  }}
                  error={Boolean(phoneError)}
                  helperText={phoneError}
                  size="small"
                  placeholder="123456789"
                  sx={{ width: '200px' }}
                  InputProps={{
                    startAdornment: (
                      <Typography sx={{ mr: 1 }}>+971</Typography>
                    ),
                  }}
                />
                <IconButton size="small" color="primary" onClick={async () => {
                  if (!validatePhoneNumber(newPhone)) {
                    setPhoneError('Phone number must start with +971 and be followed by 9 digits.');
                    return;
                  }
                  try {
                    await api.patch('/users/profile/', {
                      phone_number: newPhone
                    });
                    await checkAuth();
                    setIsEditingPhone(false);
                    setPhoneError(null);
                  } catch (error: any) {
                    setPhoneError(error.response?.data?.error || 'Failed to update phone number');
                  }
                }}>
                  <CheckIcon />
                </IconButton>
                <IconButton 
                  size="small" 
                  color="error"
                  onClick={() => {
                    setIsEditingPhone(false);
                    setNewPhone(user?.phone_number || '');
                    setPhoneError(null);
                  }}
                >
                  <CloseIcon />
                </IconButton>
              </Box>
            ) : (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <PhoneIcon sx={{ color: 'text.secondary' }} />
                  <Typography variant="body1" color="text.secondary">
                    {user?.phone_number || 'No phone number'}
                  </Typography>
                </Box>
                <Tooltip title="Edit phone number">
                  <IconButton size="small" onClick={() => setIsEditingPhone(true)}>
                    <EditIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
            )}

            <Box sx={{ display: 'flex', gap: 3, flexDirection: 'column', width: '100%' }}>
              {/* Level Progress */}
              <Box sx={{ width: '100%' }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="subtitle1" color="text.secondary">Level Progress</Typography>
                  <Typography variant="subtitle1" color="primary.main">{user?.level}</Typography>
                </Box>
                <Box sx={{ position: 'relative', height: '8px', bgcolor: 'rgba(100, 255, 218, 0.1)', borderRadius: 4, overflow: 'hidden' }}>
                  <Box
                    sx={{
                      position: 'absolute',
                      left: 0,
                      top: 0,
                      height: '100%',
                      width: `${(user?.points % 100) || 0}%`,
                      background: 'linear-gradient(90deg, #64ffda, #7b89f4)',
                      borderRadius: 4,
                      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                      '&::after': {
                        content: '""',
                        position: 'absolute',
                        right: 0,
                        height: '100%',
                        width: '4px',
                        background: 'rgba(255,255,255,0.3)',
                        transform: 'translateX(2px)',
                      }
                    }}
                  />
                </Box>
              </Box>

              {/* Stats Cards */}
              <Box
                sx={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(2, 1fr)',
                  gap: 2,
                  width: '100%',
                }}
              >
                <Paper
                  elevation={0}
                  sx={{
                    p: 2,
                    m:3,
                    textAlign: 'center',
                    background: 'linear-gradient(135deg, rgba(100, 255, 218, 0.15), rgba(123, 137, 244, 0.15))',
                    backdropFilter: 'blur(10px)',
                    border: '1px solid rgba(100, 255, 218, 0.2)',
                    borderRadius: 2,
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                   '&:hover': {
                    transform: 'translateY(-5px)',
                    boxShadow: '0 8px 24px rgba(100, 255, 218, 0.1)',
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      inset: 0,
                      borderRadius: 'inherit',
                      border: '1px solid rgba(100, 255, 218, 0.3)',
                    }
                  },
                  }}
                >
                  <StarIcon sx={{ fontSize: 30, color: 'primary.main', mb: 1 }} />
                  <Typography variant="h6" color="primary.main">{user?.level}</Typography>
                  <Typography variant="body2" color="text.secondary">Level</Typography>
                </Paper>

                <Paper
                  elevation={0}
                  sx={{
                    p: 2,
                    m:3,
                    textAlign: 'center',
                    background: 'linear-gradient(135deg, rgba(100, 255, 218, 0.15), rgba(123, 137, 244, 0.15))',
                    backdropFilter: 'blur(10px)',
                    border: '1px solid rgba(100, 255, 218, 0.2)',
                    borderRadius: 2,
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                   '&:hover': {
                  transform: 'translateY(-5px)',
                  boxShadow: '0 8px 24px rgba(100, 255, 218, 0.1)',
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    inset: 0,
                    borderRadius: 'inherit',
                    border: '1px solid rgba(100, 255, 218, 0.3)',
                  }
                }
                  }}
                >
                  <EmojiEventsIcon sx={{ fontSize: 30, color: 'secondary.main', mb: 1 }} />
                  <Typography variant="h6" color="secondary.main">{user?.points}</Typography>
                  <Typography variant="body2" color="text.secondary">Points</Typography>
                </Paper>
              </Box>

              {/* Track */}
              <Box sx={{ width: '100%', mt: 2 }}>
                <Paper
                  elevation={0}
                  sx={{
                    p: 3,
                    ml: 3,
                    background: 'linear-gradient(135deg, rgba(100, 255, 218, 0.15), rgba(123, 137, 244, 0.15))',
                    backdropFilter: 'blur(10px)',
                    border: '1px solid rgba(100, 255, 218, 0.2)',
                    borderRadius: 2,
                    mb: 2,
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <AssignmentIcon sx={{ color: 'info.main', fontSize: 24 }} />
                    <Box>
                      <Typography variant="body2" color="text.secondary">Track</Typography>
                      <Typography variant="subtitle1" color="info.main">{user?.track?.name}</Typography>
                    </Box>
                  </Box>
                </Paper>


              </Box>
            </Box>
          </Paper>
        </Grid>

        {/* Activity & Achievements */}
        <Grid item xs={12} md={8}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Paper
                elevation={0}
                sx={{
                  p: 4,
                  ml: 3,
                  mr: 10,
                  background: 'linear-gradient(135deg, rgba(100, 255, 218, 0.15), rgba(123, 137, 244, 0.15))',
                  backdropFilter: 'blur(10px)',
                  borderRadius: 4,
                  border: '1px solid rgba(100, 255, 218, 0.2)',
                  transition: 'all 0.2s ease',
                  cursor: 'pointer',
                  '&:hover': {
                    background: 'rgba(100, 255, 218, 0.05)',
                    transform: 'translateX(4px)',
                  }
                }}
              >
                <Typography variant="h5" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Box
                    sx={{
                      width: 40,
                      height: 40,
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      background: 'linear-gradient(135deg, rgba(100, 255, 218, 0.2), rgba(123, 137, 244, 0.2))',
                    }}
                  >
                    <AssignmentIcon sx={{ color: 'primary.main' }} />
                  </Box>
                  Recent Activity
                </Typography>
                <Divider sx={{ mb: 3, opacity: 0.2 }} />
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  {/* Activity Timeline */}
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box
                      sx={{
                        width: 40,
                        height: 40,
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        background: 'rgba(100, 255, 218, 0.1)',
                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                        '&:hover': {
                                transform: 'translateY(-5px)',
                                boxShadow: '0 8px 24px rgba(100, 255, 218, 0.1)',
                                '&::before': {
                                  content: '""',
                                  position: 'absolute',
                                  inset: 0,
                                  borderRadius: 'inherit',
                                  border: '1px solid rgba(100, 255, 218, 0.3)',
                                }
                              }
                      }}
                    >
                      <EmojiEventsIcon sx={{ color: 'primary.main' }} />
                    </Box>
                    <Box sx={{ flex: 1 }}>
                      <Typography variant="subtitle1">Reached Level {user?.level}</Typography>
                      <Typography variant="body2" color="text.secondary">Keep going! You're making great progress.</Typography>
                    </Box>
                  </Box>

                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Box
                      sx={{
                        width: 40,
                        height: 40,
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        background: 'rgba(123, 137, 244, 0.1)',
                      }}
                    >
                      <AssignmentIcon sx={{ color: 'secondary.main' }} />
                    </Box>
                    <Box sx={{ flex: 1 }}>
                      <Typography variant="subtitle1">Earned {user?.points} Points</Typography>
                      <Typography variant="body2" color="text.secondary">Through project submissions and evaluations</Typography>
                    </Box>
                  </Box>
                </Box>
              </Paper>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Box>

      {/* Image Upload Dialog */}
      <Dialog 
        open={openDialog} 
        onClose={handleCloseDialog}
        PaperProps={{
          sx: {
            width: '100%',
            maxWidth: 500,
          }
        }}
      >
        <DialogTitle>Update Profile Picture</DialogTitle>
        <DialogContent>
          {error && (
            <Alert 
              severity="error" 
              sx={{ mb: 2 }}
              action={
                <IconButton
                  aria-label="close"
                  color="inherit"
                  size="small"
                  onClick={() => setError(null)}
                >
                  <CloseIcon fontSize="inherit" />
                </IconButton>
              }
            >
              {error}
            </Alert>
          )}
          {previewUrl && (
            <Box sx={{ mt: 2 }}>
              <img
                src={previewUrl}
                alt="Preview"
                style={{ maxWidth: '100%', maxHeight: 300, objectFit: 'contain' }}
              />
              <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                {uploading 
                  ? 'Uploading in progress... Click Cancel to stop the upload'
                  : error
                  ? 'Click Try Again to select a different image'
                  : 'Click Upload to confirm or Cancel to choose a different image'
                }
              </Typography>
              {uploading && (
                <Box sx={{ width: '100%', mt: 2 }}>
                  <LinearProgress 
                    variant="determinate" 
                    value={uploadProgress}
                    sx={{
                      height: 8,
                      borderRadius: 4,
                      backgroundColor: 'rgba(0, 0, 0, 0.1)',
                      '& .MuiLinearProgress-bar': {
                        borderRadius: 4,
                      }
                    }}
                  />
                  <Typography variant="caption" color="text.secondary" align="center" sx={{ mt: 0.5, display: 'block' }}>
                    {uploadProgress}% uploaded
                  </Typography>
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{ p: 2, gap: 1 }}>
          <Button 
            onClick={() => {
              if (error) {
                // For Try Again, close immediately and trigger file input
                handleCloseDialog();
                setTimeout(() => fileInputRef.current?.click(), 100);
              } else {
                handleCloseDialog();
              }
            }} 
            color="error"
            variant={uploading ? "contained" : "outlined"}
            fullWidth
            sx={{
              minWidth: 100,
              ...(uploading ? {
                bgcolor: 'error.main',
                '&:hover': {
                  bgcolor: 'error.dark',
                }
              } : {
                '&:hover': {
                  bgcolor: 'error.main',
                  color: 'white',
                }
              })
            }}
          >
            {uploading ? 'Cancel Upload' : error ? 'Try Again' : 'Cancel'}
          </Button>
          {!error && (
            <Button
              onClick={handleUpload}
              variant="contained"
              color="primary"
              disabled={uploading}
              startIcon={uploading && <CircularProgress size={20} sx={{ color: 'background.default' }} />}
              sx={{ minWidth: 100 }}
            >
              {uploading ? `Uploading ${uploadProgress}%` : 'Upload'}
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </>
  );
};

export default Profile;