# Generated by Django 4.2 on 2025-02-02 19:10

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('projects', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='project',
            name='evaluation_markdown',
        ),
        migrations.AddField(
            model_name='evaluation',
            name='score',
            field=models.FloatField(null=True),
        ),
        migrations.AddField(
            model_name='project',
            name='passing_score',
            field=models.FloatField(default=70.0, help_text='Minimum score (%) required to pass this project'),
        ),
        migrations.CreateModel(
            name='Question',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('text', models.TextField()),
                ('response', models.BooleanField(default=False)),
                ('weight', models.FloatField(default=1, help_text='Question weight (relative importance)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='questions', to='projects.project')),
            ],
        ),
    ]
