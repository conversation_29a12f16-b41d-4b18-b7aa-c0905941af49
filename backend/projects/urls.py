from django.urls import path
from . import views

app_name = 'projects'

urlpatterns = [
    path('cancel-upload/', views.CancelUploadView.as_view(), name='cancel-upload'),
    path('my-submissions/', views.UserProjectSubmissionsView.as_view(), name='my-submissions'),
    path('submit/', views.ProjectSubmissionView.as_view(), name='submit'),
    path('pool/', views.EvaluationPoolView.as_view(), name='evaluation-pool'),
    path('evaluation/<int:pk>/', views.EvaluationDetailView.as_view(), name='evaluation-detail'),
    path('evaluate/<int:pk>/', views.EvaluationView.as_view(), name='evaluate'),
    path('submission/<int:pk>/status/', views.SubmissionStatusView.as_view(), name='submission-status'),
    path('<int:pk>/', views.ProjectDetailView.as_view(), name='project-detail'),
    path('<int:project_id>/questions/', views.QuestionCreateView.as_view(), name='question-create'),

    # Course-related URLs - more specific routes first
    path('courses/register/<str:course_code>/', views.CourseRegisterView.as_view(), name='course-register'),
    path('courses/registrations/<int:request_id>/approve/', views.CourseRegistrationApproveView.as_view(), name='course-registration-approve'),
    path('courses/registrations/<int:request_id>/reject/', views.CourseRegistrationRejectView.as_view(), name='course-registration-reject'),
    path('courses/', views.CourseListCreateView.as_view(), name='course-list-create'),

    # Course URLs with parameters - these come after specific routes
    path('courses/<str:course_id>/projects/', views.ProjectListCreateView.as_view(), name='course-project-list-create'),
    path('courses/<str:course_id>/next/', views.NextProjectView.as_view(), name='course-next-project'),
    path('courses/<str:pk>/registrations/', views.CourseRegistrationListView.as_view(), name='course-registration-list'),
    path('courses/<str:pk>/', views.CourseDetailView.as_view(), name='course-detail'),

    # Project creation endpoint for educators
    path('', views.ProjectCreateView.as_view(), name='project-create'),
]
