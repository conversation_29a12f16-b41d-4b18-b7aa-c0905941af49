from rest_framework import serializers
from django.utils import timezone
from .models import Project, ProjectSubmission, Evaluation , Question, Course, CourseRegistrationRequest
from users.models import Track
from users.serializers import UserSerializer, TrackSerializer

class QuestionSerializer(serializers.ModelSerializer):
    percentage = serializers.SerializerMethodField()

    class Meta:
        model = Question
        fields = ['id', 'text', 'response', 'weight', 'percentage', 'created_at', 'updated_at']

    def get_percentage(self, obj):
        return round(obj.get_weighted_score(), 2)

class QuestionResponseSerializer(serializers.Serializer):
    question_id = serializers.IntegerField()
    response = serializers.BooleanField()

class EvaluationSerializer(serializers.ModelSerializer):
    evaluator = UserSerializer(read_only=True)
    question_responses = QuestionResponseSerializer(many=True, write_only=True)
    score = serializers.FloatField(read_only=True)

    class Meta:
        model = Evaluation
        fields = ['id', 'comments', 'is_approved', 'evaluator',
                 'created_at','score' ,'question_responses']
        read_only_fields = ('evaluator',)

    def create(self, validated_data):
        # Remove question_responses from validated_data since it's not a model field
        validated_data.pop('question_responses', None)
        return super().create(validated_data)

class ProjectSerializer(serializers.ModelSerializer):
    questions = QuestionSerializer(many=True, read_only=True)
    can_submit = serializers.SerializerMethodField()
    prerequisite = serializers.PrimaryKeyRelatedField(queryset=Project.objects.all(), required=False, allow_null=True, write_only=True)
    prerequisite_detail = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Project
        fields = ['id', 'title', 'description', 'pdf_file', 
                 'points_required', 'passing_score', 'created_at', 
                 'updated_at', 'questions', 'can_submit', 'course', 'prerequisite', 'prerequisite_detail']

    def get_can_submit(self, obj):
        request = self.context.get('request')
        if not request or not request.user.is_authenticated:
            return False
        user = request.user
        prerequisite = obj.prerequisite
        if prerequisite:
            has_completed = ProjectSubmission.objects.filter(
                submitted_by=user,
                project=prerequisite,
                status='completed'
            ).exists()
            return has_completed
        return True

    def to_representation(self, instance):
        data = super().to_representation(instance)
        request = self.context.get('request')
        if request and instance.pdf_file:
            data['pdf_file'] = request.build_absolute_uri(instance.pdf_file.url)
        return data

    def get_prerequisite_detail(self, obj):
        if obj.prerequisite:
            return ProjectSerializer(obj.prerequisite, context=self.context).data
        return None

class ProjectSubmissionSerializer(serializers.ModelSerializer):
    project = ProjectSerializer(read_only=True)
    submitted_by = UserSerializer(read_only=True)
    project_id = serializers.PrimaryKeyRelatedField(
        queryset=Project.objects.all(),
        source='project',
        write_only=True
    )
    evaluation = serializers.SerializerMethodField()
    evaluations = EvaluationSerializer(many=True, read_only=True)
    evaluation_progress = serializers.SerializerMethodField()
    submitted_by_details = serializers.SerializerMethodField()
    assigned_evaluator = UserSerializer(read_only=True)

    class Meta:
        model = ProjectSubmission
        fields = ['id', 'project', 'project_id', 'submitted_by', 
                 'github_repo', 'zip_file', 'submission_type', 'status', 'created_at', 'updated_at',
                 'evaluation', 'evaluations', 'evaluation_progress', 'final_score', 'submitted_by_details',
                 'assigned_evaluator']
        read_only_fields = ('submitted_by', 'status', 'project')

    def get_evaluation_progress(self, obj):
        return {
            'current': obj.evaluations.count(),
            'required': obj.project.required_evaluators,
            'is_complete': obj.evaluations.count() >= obj.project.required_evaluators
        }

    def get_evaluation(self, obj):
        try:
            evaluation = obj.evaluations.first()  # Get the latest evaluation
            if evaluation:
                return {
                    'score': evaluation.score,
                    'comments': evaluation.comments,
                    'is_approved': evaluation.is_approved,
                    'created_at': evaluation.created_at
                }
            return None
        except Evaluation.DoesNotExist:
            return None

    def validate(self, attrs):
        submission_type = attrs.get('submission_type')
        github_repo = attrs.get('github_repo')
        zip_file = attrs.get('zip_file')

        if submission_type == 'github' and not github_repo:
            raise serializers.ValidationError({'github_repo': 'GitHub repository URL is required for GitHub submissions'})
        elif submission_type == 'zip' and not zip_file:
            raise serializers.ValidationError({'zip_file': 'ZIP file is required for ZIP submissions'})

        return attrs

    def get_submitted_by_details(self, obj):
        user = obj.submitted_by
        if user:
            return {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'full_name': f"{user.first_name} {user.last_name}".strip(),
                'submitted_at': obj.created_at
            }
        return None

    def create(self, validated_data):
        # The project_id field with source='project' automatically converts to project object
        # So we don't need to manually handle project_id here
        return ProjectSubmission.objects.create(**validated_data)

class CourseSerializer(serializers.ModelSerializer):
    educator = UserSerializer(read_only=True)
    track = TrackSerializer(read_only=True)
    track_id = serializers.PrimaryKeyRelatedField(
        queryset=Track.objects.all(),
        write_only=True,
        source='track',
        required=False
    )
    student_count = serializers.SerializerMethodField()
    evaluations_completed = serializers.SerializerMethodField()
    evaluations_pending = serializers.SerializerMethodField()
    progress_percent = serializers.SerializerMethodField()
    last_active = serializers.SerializerMethodField()
    status = serializers.SerializerMethodField()

    class Meta:
        model = Course
        fields = [
            'code', 'name', 'description', 'educator', 'track', 'track_id', 
            'created_at', 'updated_at', 'student_count', 'evaluations_completed',
            'evaluations_pending', 'progress_percent', 'last_active', 'status'
        ]
        read_only_fields = ('code', 'educator')
        extra_kwargs = {
            'name': {'required': True},
            'description': {'required': True}
        }
    
    def get_student_count(self, obj):
        return obj.enrolled_students.count()

    def get_evaluations_completed(self, obj):
        return ProjectSubmission.objects.filter(
            project__course=obj,
            status='completed'
        ).count()

    def get_evaluations_pending(self, obj):
        return ProjectSubmission.objects.filter(
            project__course=obj,
            status__in=['pending', 'in_evaluation']
        ).count()

    def get_progress_percent(self, obj):
        total_submissions = ProjectSubmission.objects.filter(
            project__course=obj
        ).count()
        if total_submissions == 0:
            return 0
        completed = ProjectSubmission.objects.filter(
            project__course=obj,
            status='completed'
        ).count()
        return int((completed / total_submissions) * 100) if total_submissions > 0 else 0

    def get_last_active(self, obj):
        latest_submission = ProjectSubmission.objects.filter(
            project__course=obj
        ).order_by('-created_at').first()
        return latest_submission.created_at.date().isoformat() if latest_submission else None

    def get_status(self, obj):
        # A course is considered active if it has enrolled students
        if obj.enrolled_students.exists():
            return "active"
        return "draft"

    def validate(self, attrs):
        # # Ensure track is provided when creating a course
        # if self.instance is None and 'track' not in attrs:  # Creating new course
        #     # assigning default track if not provided for demo purposes
        #     attrs['track'] = Track.objects.first()  # You can change this logic as needed
        #     # Uncomment the next line if you want to enforce track requirement
        #     # raise serializers.ValidationError({'track_id': 'Track is required when creating a course'})
        return super().validate(attrs)
    
    def create(self, validated_data):
        # Ensure code is not provided and will be auto-generated
        validated_data.pop('code', None)
        # Set the current user as the educator
        request = self.context.get('request')
        if request and hasattr(request, 'user'):
            validated_data['educator'] = request.user
        return super().create(validated_data)

class CourseRegistrationRequestSerializer(serializers.ModelSerializer):
    student = UserSerializer(read_only=True)
    course = CourseSerializer(read_only=True)

    class Meta:
        model = CourseRegistrationRequest
        fields = ['id', 'student', 'course', 'status', 'created_at', 'updated_at']