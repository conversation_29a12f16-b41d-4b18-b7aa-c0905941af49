from django.core.management.base import BaseCommand
from django.db.models import Count, Avg, Q, Sum, Max
from django.db.models.functions import TruncMonth
from django.utils import timezone
from users.models import User, Zone
from projects.models import ProjectSubmission, Evaluation
from datetime import timed<PERSON><PERSON>
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
import os
import base64
from users.email_utils import send_brevo_email

class Command(BaseCommand):
    help = 'Generate comprehensive user analytics report'

    def style_worksheet(self, ws):
        header_fill = PatternFill(start_color='B3E0FF', end_color='B3E0FF', fill_type='solid')
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        for row in ws.rows:
            for cell in row:
                cell.border = border
                if cell.row == 1:
                    cell.font = Font(bold=True)
                    cell.fill = header_fill
                    cell.alignment = Alignment(horizontal='center')

        # Adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            for cell in column:
                try:
                    max_length = max(max_length, len(str(cell.value)))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

    def generate_zone_report(self, zone, timestamp=None, output_dir='reports'):
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        if timestamp is None:
            timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
            
        filename = f'{output_dir}/zone_{zone.name}_analytics_{timestamp}.xlsx'
        wb = Workbook()
        ws = wb.active
        ws.title = "User Analytics"

        # Updated headers with email info
        headers = [
            "Username",
            "First Name",
            "Last Name",
            "Email",
            "Level",
            "Track",
            "Total Submissions",
            "Completed Projects",
            "Success Rate",
            "Average Score",
            "Evaluations Given",
            "Last Active",
            "Activity Score"
        ]
        ws.append(headers)

        # Get all users in the zone with their key metrics
        users = User.objects.filter(zone=zone).select_related('track')
        now = timezone.now()

        for user in users:
            submissions = ProjectSubmission.objects.filter(submitted_by=user)
            completed_submissions = submissions.filter(status='completed')
            evaluations = Evaluation.objects.filter(evaluator=user)
            
            # Calculate key metrics
            total_submissions = submissions.count()
            completed_count = completed_submissions.count()
            success_rate = (completed_count / total_submissions * 100) if total_submissions > 0 else 0
            avg_score = completed_submissions.aggregate(Avg('final_score'))['final_score__avg'] or 0
            
            # Calculate activity score (weighted metric of user engagement)
            activity_score = (
                completed_count * 10 +  # Weight completed projects heavily
                evaluations.count() * 5 +  # Weight evaluations
                user.level * 3  # Include level instead of points
            )

            row = [
                user.username,
                user.first_name,
                user.last_name,
                user.email,
                user.level,
                user.track.name if user.track else "No Track",
                total_submissions,
                completed_count,
                f"{success_rate:.1f}%",
                f"{avg_score:.1f}%",
                evaluations.count(),
                user.last_login.strftime('%Y-%m-%d') if user.last_login else "Never",
                activity_score
            ]
            ws.append(row)

        self.style_worksheet(ws)

        # Add summary sheet with updated metrics
        summary_ws = wb.create_sheet("Zone Summary")
        total_users = users.count()
        active_users = users.filter(last_login__gte=now - timedelta(days=30)).count()
        
        summary_data = [
            ["Zone Analytics Summary", ""],
            ["Metric", "Value"],
            ["Total Users", total_users],
            ["Active Users (30 days)", active_users],
            ["Average User Level", f"{users.aggregate(Avg('level'))['level__avg']:.1f}"],
            ["Total Projects Completed", ProjectSubmission.objects.filter(submitted_by__zone=zone, status='completed').count()],
            ["Total Evaluations Given", Evaluation.objects.filter(evaluator__zone=zone).count()],
            ["Average Success Rate", f"{(ProjectSubmission.objects.filter(submitted_by__zone=zone, status='completed').count() / ProjectSubmission.objects.filter(submitted_by__zone=zone).count() * 100) if ProjectSubmission.objects.filter(submitted_by__zone=zone).count() > 0 else 0:.1f}%"],
        ]
        
        for row in summary_data:
            summary_ws.append(row)
        
        self.style_worksheet(summary_ws)

        wb.save(filename)
        self.stdout.write(
            self.style.SUCCESS(f'Generated analytics report for zone {zone.name}: {filename}')
        )

    def handle(self, *args, **options):
        # Create reports directory if it doesn't exist
        if not os.path.exists('reports'):
            os.makedirs('reports')
            
        # Delete old reports
        for old_file in os.listdir('reports'):
            if old_file.endswith('.xlsx'):
                os.remove(os.path.join('reports', old_file))

        zones = Zone.objects.all()
        if not zones.exists():
            self.stdout.write(self.style.WARNING('No zones found in the database'))
            return

        generated_files = []
        timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
        zone_names = []
        for zone in zones:
            try:
                filename = f'reports/zone_{zone.name}_analytics_{timestamp}.xlsx'
                self.generate_zone_report(zone, timestamp=timestamp)
                generated_files.append(filename)
                zone_names.append(zone.name)
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Error generating report for zone {zone.name}: {str(e)}')
                )

        # Send email with attachments
        try:
            if generated_files:
                attachments = []
                for file_path in generated_files:
                    with open(file_path, 'rb') as f:
                        content = base64.b64encode(f.read()).decode()
                        attachments.append({
                            'content': content,
                            'name': os.path.basename(file_path)
                        })

                # Format date for email
                today = timezone.now().strftime('%B %d, %Y')
                
                # Create email subject and content
                subject = f'Dev Space Analytics Report - {today}'
                html_content = f"""
                <h2>Dev Space Analytics Report - {today}</h2>
                
                <p>Hello,</p>
                
                <p>Please find attached the latest analytics reports for the following zones:</p>
                <ul>
                    {''.join(f'<li>Zone {name}</li>' for name in zone_names)}
                </ul>

                <p>Each report includes:</p>
                <ul>
                    <li>User activity metrics</li>
                    <li>Project completion rates</li>
                    <li>Evaluation statistics</li>
                    <li>Zone-wide performance indicators</li>
                </ul>

                <p>For any questions or concerns about these reports, please contact the Dev Space team.</p>

                <p>Best regards,<br>
                Dev Space Team</p>
                """

                # Send email with reports
                send_brevo_email(
                    to_emails=['<EMAIL>'],
                    subject=subject,
                    html_content=html_content,
                    attachments=attachments
                )
                self.stdout.write(self.style.SUCCESS('Reports sent successfully via email'))
            else:
                self.stdout.write(self.style.WARNING('No reports were generated to send'))
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error sending email with reports: {str(e)}')
            )
