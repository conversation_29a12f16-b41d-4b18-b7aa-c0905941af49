from rest_framework import permissions
from users.models import User

class IsApprovedUser(permissions.BasePermission):
    def has_permission(self, request, view):
        # Handle anonymous users
        if not request.user or not request.user.is_authenticated:
            return False
            
        # Django REST Framework already loads the user instance
        # so we can directly check the attributes without an extra query
        return request.user.is_approved and request.user.is_email_verified
