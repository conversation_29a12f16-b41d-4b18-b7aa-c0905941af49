from django.utils.deprecation import MiddlewareMixin
from users.models import User

class UserMiddleware(MiddlewareMixin):
    def process_request(self, request):
        if hasattr(request, 'user') and request.user.is_authenticated:
            try:
                old_user_id = request.user.id
                request.user = User.objects.get(id=request.user.id)
                print(f"Middleware: User {old_user_id} refreshed from database")
            except User.DoesNotExist:
                print(f"Middleware: User {request.user.id} not found in database")
                pass